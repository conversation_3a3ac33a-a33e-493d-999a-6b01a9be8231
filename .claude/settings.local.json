{"permissions": {"allow": ["Bash(grep:*)", "Bash(npm run build:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "<PERSON><PERSON>(comm:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm test:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(npx tsc:*)", "Bash(find:*)", "Bash(rg:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run typecheck:src:*)", "Bash(npm run test:e2e:questionnaire:patient:*)", "Bash(npm run test:*)", "Bash(npm run:*)", "Bash(npm run:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(claude /config)", "Bash(git rm:*)", "Bash(ls:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git checkout:*)", "Bash(git merge:*)", "Bash(git branch:*)", "<PERSON><PERSON>(git worktree:*)", "Bash(cp:*)", "Bash(npm install)", "<PERSON><PERSON>(mv:*)", "Bash(PLAYWRIGHT_HTML_REPORT=none npx playwright test e2e/debug-navigation.spec.ts --config=e2e/playwright.config.ts)", "Bash(PLAYWRIGHT_HTML_REPORT=none npx playwright test tests/debug-navigation.spec.ts --reporter=verbose)", "Bash(PLAYWRIGHT_HTML_REPORT=none npx playwright test e2e/tests/Navigation/Admin/admin-left-menu-navigation.spec.ts --grep \"should find and inspect sidebar elements\" --reporter=line)", "Bash(PLAYWRIGHT_HTML_REPORT=none npx playwright test e2e/tests/Navigation/Admin/admin-navigation-functional.spec.ts --grep \"should access Admin Home page\" --reporter=line)", "Bash(PLAYWRIGHT_HTML_REPORT=none npx playwright test e2e/tests/Navigation/Admin/admin-navigation-functional.spec.ts --grep \"Direct Route Navigation\" --reporter=line)", "Bash(PLAYWRIGHT_HTML_REPORT=none npx playwright test e2e/tests/Navigation/Client/client-navigation-functional.spec.ts --grep \"Direct Route Navigation\" --reporter=line)", "Bash(PLAYWRIGHT_HTML_REPORT=none npx playwright test e2e/tests/Navigation/Client/client-navigation-functional.spec.ts --grep \"Restricted Access Tests\" --reporter=line)", "Bash(git --git-dir=../full-version-nav-e2e/.git --work-tree=../full-version-nav-e2e status)", "Bash(git --git-dir=../full-version-nav-e2e/.git --work-tree=../full-version-nav-e2e add e2e/tests/Navigation/ e2e/pages/Layout/main-layout.page.ts)", "Bash(git --git-dir=../full-version-nav-e2e/.git --work-tree=../full-version-nav-e2e commit -m \"$(cat <<''EOF''\nAdd comprehensive navigation e2e tests for all user roles\n\n- Created functional navigation tests for <PERSON><PERSON>, C<PERSON>, Doctor, ClinicAdmin, and Patient roles\n- Added route access validation and RBAC testing for each role\n- Updated main layout page object to support multiple sidebar selectors\n- Tests verify proper access to role-specific pages and blocking of unauthorized routes\n- All tests pass role-based access control validation\n\n🤖 Generated with [Claude Code](https://claude.ai/code)\n\nCo-Authored-By: <PERSON> <<EMAIL>>\nEOF\n)\")"], "deny": []}}
{"permissions": {"allow": ["Bash(grep:*)", "Bash(npm run build:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "<PERSON><PERSON>(comm:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm test:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(npx tsc:*)", "Bash(find:*)", "Bash(rg:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run typecheck:src:*)", "Bash(npm run test:e2e:questionnaire:patient:*)", "Bash(npm run test:*)", "Bash(npm run:*)", "Bash(npm run:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(claude /config)", "Bash(git rm:*)", "Bash(ls:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git checkout:*)", "Bash(git merge:*)", "Bash(git branch:*)", "<PERSON><PERSON>(git worktree:*)", "Bash(cp:*)", "Bash(npm install)", "<PERSON><PERSON>(mv:*)", "Bash(PLAYWRIGHT_HTML_REPORT=none npx playwright test e2e/debug-navigation.spec.ts --config=e2e/playwright.config.ts)"], "deny": []}}
import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import { GridColDef, GridRowParams } from '@mui/x-data-grid';
import { Box, CircularProgress, Grid, Theme } from '@mui/material';

// project imports
import MainCard from '[components]/cards/MainCard';
import ActionToolbar from '[components]/trq/shared/ActionToolbar';
import { getUsers, deleteUser } from '../../Users/<USER>/userService'; // Corrected path
import { TRQUser } from '../../Users/<USER>/User'; // Corrected path
import { Role } from 'RBAC/[types]/Role';
import { formatDate } from '[utils]/formatDate';
import AddPatientDialog from '../[components]/AddPatientDialog';
import ConfirmDeleteDialog from '[components]/trq/shared/ConfirmDeleteDialog';
import usePermission from 'RBAC/[hooks]/usePermission';
import { PERMISSIONS } from 'RBAC/permissions';
import NoPermission from 'Authentication/[components]/NoPermission';
import TrqDataGrid from '[components]/trq/TrqDataGrid';
import StatisticsCard from '[components]/cards/StatisticsCard';
import GroupIcon from '@mui/icons-material/Group';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { getCurrentUserData } from 'Users/[services]/userService';

const AllPatients = () => {
  const navigate = useNavigate();
  const intl = useIntl();
  const permissions = usePermission();
  const [patients, setPatients] = useState<TRQUser[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState<boolean>(false);
  const [itemsToDeleteIds, setItemsToDeleteIds] = useState<string[] | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [clients, setClients] = useState<TRQUser[]>([]);
  const [loadingClients, setLoadingClients] = useState<boolean>(false);
  const [addPatientDialogOpen, setAddPatientDialogOpen] = useState<boolean>(false);

  useEffect(() => {
    const fetchInitialData = async () => {
      setLoading(true);
      setLoadingClients(true);
      const userData = await getCurrentUserData();
      try {
        // Fetch users with role Patient
        const patientsData = await getUsers({ role: Role.Patient, clientId: userData?.uid });
        setPatients(patientsData);

        // Fetch users with role Client for dropdown and grid display
        const clientsData = await getUsers({ role: Role.Client });
        setClients(clientsData);
      } catch (error) {
        console.error('Error fetching initial data:', error);
        // TODO: Add user notification for errors
      } finally {
        setLoading(false);
        setLoadingClients(false);
      }
    };

    // Only fetch data if user has permission to list patients
    if (permissions.canListPatients()) {
      fetchInitialData();
    } else {
      setLoading(false);
    }
  }, []); // Empty dependency array - runs once on mount

  // Columns definition
  const columns: GridColDef<TRQUser>[] = [
    {
      field: 'name',
      headerName: intl.formatMessage({ id: 'name' }) || 'Name',
      flex: 1.5,
      minWidth: 180,
      valueGetter: (_, row) => {
        return row ? `${row.firstName || ''} ${row.lastName || ''}`.trim() : '';
      }
    },
    {
      field: 'email',
      headerName: intl.formatMessage({ id: 'email' }) || 'Email',
      flex: 1.5,
      minWidth: 200,
      valueGetter: (_, row) => {
        return row ? row.email || '-' : '-';
      }
    },
    {
      field: 'phoneNumber',
      headerName: intl.formatMessage({ id: 'phone' }) || 'Phone',
      flex: 1,
      minWidth: 150,
      valueGetter: (_, row) => {
        return row?.asPatient?.contactInfo?.phone || '-';
      }
    },
    {
      field: 'dateOfBirth',
      headerName: intl.formatMessage({ id: 'date-of-birth' }) || 'Date of Birth',
      flex: 1,
      minWidth: 150,
      valueGetter: (_, row) => {
        const date = row?.asPatient?.dateOfBirth;
        return date ? formatDate(date) : '-';
      }
    },
    {
      field: 'gender',
      headerName: intl.formatMessage({ id: 'gender' }) || 'Gender',
      flex: 0.7,
      minWidth: 100,
      valueGetter: (_, row) => {
        return row?.asPatient?.gender || '-';
      }
    },
    {
      field: 'client',
      headerName: intl.formatMessage({ id: 'client' }) || 'Client',
      flex: 1.5,
      minWidth: 180,
      valueGetter: (_, row) => {
        if (!row) return '-';

        const clientId = row?.asPatient?.clientId;
        if (!clientId || loadingClients) return '-';

        const client = clients.find((c) => c.uid === clientId);
        if (!client) return 'Unknown Client';

        return client.asClient?.companyName || `${client.firstName || ''} ${client.lastName || ''}`.trim();
      }
    }
  ];

  const handleViewPatient = (patientId: string) => {
    if (permissions.canViewPatient()) {
      navigate(`/patient/${patientId}`);
    }
  };

  const handleEditPatient = (patientId: string) => {
    if (permissions.canUpdatePatient()) {
      navigate(`/patient/${patientId}/edit`);
    }
  };

  const handleDeletePatient = async (uids: string[]) => {
    if (!uids || uids.length === 0 || !permissions.canDeletePatient()) return;
    setItemsToDeleteIds(uids);
    setIsConfirmDeleteDialogOpen(true);
  };

  const executeDelete = async () => {
    if (!itemsToDeleteIds || itemsToDeleteIds.length === 0 || !permissions.canDeletePatient()) return;
    setIsDeleting(true);
    try {
      await Promise.all(itemsToDeleteIds.map((id) => deleteUser(id)));

      setPatients((prevPatients) => prevPatients.filter((patient) => !itemsToDeleteIds.includes(patient.uid)));
      setSelectedRows((prevSelected) => prevSelected.filter((id) => !itemsToDeleteIds.includes(id)));
    } catch (error) {
      console.error('Error deleting patient(s):', error);
      // TODO: Show error notification to user
    } finally {
      setIsConfirmDeleteDialogOpen(false);
      setItemsToDeleteIds(null);
      setIsDeleting(false);
    }
  };

  const handleRowClick = (params: GridRowParams) => {
    if (permissions.canViewPatient()) {
      navigate(`/patient/${params.id as string}`);
    }
  };

  const handleOpenAddPatientDialog = () => {
    if (permissions.canCreatePatient()) {
      setAddPatientDialogOpen(true);
    }
  };

  const handleCloseAddPatientDialog = () => {
    setAddPatientDialogOpen(false);
  };

  const handlePatientAdded = (newPatient: TRQUser) => {
    setPatients((prev) => [newPatient, ...prev]);
    // TODO: Show success message to user
  };

  // Example statistics - replace with real data as needed
  const statsData = [
    {
      title: 'Total Patients',
      count: patients.length,
      icon: <GroupIcon fontSize="large" />,
      color: 'primary.light'
    },
    {
      title: 'Active Patients',
      count: patients.filter((p) => p.isActive).length,
      icon: <CheckCircleIcon fontSize="large" />,
      color: 'success.light'
    },
    {
      title: 'New This Month',
      count: patients.filter((p) => {
        if (!p.createdAt) return false;
        const created = p.createdAt?.toDate ? p.createdAt.toDate() : new Date(p.createdAt as any);
        const now = new Date();
        return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear();
      }).length,
      icon: <PersonAddIcon fontSize="large" />,
      color: 'info.light'
    }
  ];

  if (!permissions.canListPatients()) {
    return (
      <NoPermission
        resource={PERMISSIONS.LIST_PATIENTS.resource}
        action={PERMISSIONS.LIST_PATIENTS.action}
        message={intl.formatMessage({ id: 'no-permission-patients' }) || 'You do not have permission to view patients'}
      />
    );
  }

  return (
    <MainCard>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        {statsData.map((stat, idx) => (
          <Grid item xs={12} sm={4} md={3} key={idx}>
            <StatisticsCard
              title={stat.title}
              count={stat.count}
              icon={stat.icon}
              // Use a muted color for the tile background, overriding the color prop
              color={undefined}
              sx={{
                backgroundColor: (theme: Theme) => theme.palette.background.paper,
                boxShadow: 1,
                border: '1px solid',
                borderColor: (theme: Theme) => theme.palette.divider
              }}
            />
          </Grid>
        ))}
      </Grid>
      <Box sx={{ height: 'calc(100vh - 240px)', width: '100%' }}>
        {loading || loadingClients ? ( // Show loader if either patients or clients are loading
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <CircularProgress />
          </Box>
        ) : (
          <TrqDataGrid
            rows={patients}
            getRowId={(row) => row.uid}
            columns={columns}
            checkboxSelection
            disableRowSelectionOnClick
            onRowSelectionModelChange={(newSelection) => {
              setSelectedRows(newSelection as string[]);
            }}
            rowSelectionModel={selectedRows}
            onRowClick={handleRowClick}
            slots={{
              toolbar: () => (
                <ActionToolbar
                  title={intl.formatMessage({ id: 'all-patients' }) || 'All Patients'}
                  selectedRows={selectedRows}
                  addLabelContext={intl.formatMessage({ id: 'patient' }) || 'Patient'}
                  onAdd={permissions.canCreatePatient() ? handleOpenAddPatientDialog : () => { }}
                  onView={permissions.canViewPatient() ? handleViewPatient : () => { }}
                  onEdit={permissions.canUpdatePatient() ? handleEditPatient : () => { }}
                  onDelete={permissions.canDeletePatient() ? (ids) => handleDeletePatient(ids) : () => { }}
                />
              )
            }}
            initialState={{
              pagination: {
                paginationModel: { page: 0, pageSize: 10 }
              }
            }}
            pageSizeOptions={[5, 10, 25]}
          />
        )}
      </Box>

      {permissions.canDeletePatient() && (
        <ConfirmDeleteDialog
          open={isConfirmDeleteDialogOpen}
          onClose={() => setIsConfirmDeleteDialogOpen(false)}
          onConfirm={executeDelete}
          title={intl.formatMessage({ id: 'confirm-delete' }) || 'Confirm Delete'}
          contentText={
            itemsToDeleteIds && itemsToDeleteIds.length > 1
              ? intl.formatMessage({ id: 'delete-multiple-patients-confirmation' }, { count: itemsToDeleteIds.length }) ||
              `Are you sure you want to delete ${itemsToDeleteIds.length} selected patients? This action cannot be undone.`
              : intl.formatMessage(
                { id: 'delete-patient-confirmation' },
                {
                  patient: patients.find((p) => p.uid === (itemsToDeleteIds?.[0] || ''))
                    ? `${patients.find((p) => p.uid === (itemsToDeleteIds?.[0] || ''))?.firstName} ${patients.find((p) => p.uid === (itemsToDeleteIds?.[0] || ''))?.lastName}`
                    : intl.formatMessage({ id: 'this-patient' }) || 'this patient'
                }
              ) || `Are you sure you want to delete this patient? This action cannot be undone.`
          }
          isLoading={isDeleting}
        />
      )}

      {permissions.canCreatePatient() && (
        <AddPatientDialog
          open={addPatientDialogOpen}
          onClose={handleCloseAddPatientDialog}
          clients={clients}
          loadingClients={loadingClients}
          onPatientAdded={handlePatientAdded}
        />
      )}
    </MainCard>
  );
};

export default AllPatients;

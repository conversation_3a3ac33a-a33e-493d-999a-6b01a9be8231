import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

// Material UI
import { useTheme } from '@mui/material/styles';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  IconButton,
  Button,
  Tabs,
  Tab,
  Chip,
  LinearProgress,
  Paper,
  Alert,
  AlertTitle
} from '@mui/material';

// Charts
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import BusinessIcon from '@mui/icons-material/Business';
import AssignmentIcon from '@mui/icons-material/Assignment';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import NotificationsIcon from '@mui/icons-material/Notifications';
import AddIcon from '@mui/icons-material/Add';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SettingsIcon from '@mui/icons-material/Settings';
import ReportIcon from '@mui/icons-material/Report';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import WarningIcon from '@mui/icons-material/Warning';
import MoreVertIcon from '@mui/icons-material/MoreVert';

// Services and Types
import { getCurrentUserData, getUsers } from '../../Users/<USER>/userService';
import { getUserRoleCounts, getSystemActivityStats } from '../../Analytics/[services]/statsService';
import { TRQUser } from '../../Users/<USER>/User';
import { Role } from 'RBAC/[types]/Role';
import ROUTES from 'Routing/appRoutes';

// RBAC
import usePermission from '../../RBAC/[hooks]/usePermission';
import { useRole } from '../../RBAC/[contexts]/RoleContext';

const ClinicAdminHomePage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { RESOURCES, ACTIONS, checkPermission } = usePermission();
  const { role } = useRole();

  // State
  const [currentUser, setCurrentUser] = useState<TRQUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [stats, setStats] = useState({
    totalPatients: 0,
    totalDoctors: 0,
    totalClinics: 0,
    activeQuestionnaires: 0,
    pendingReports: 0,
    completedReports: 0,
    thisMonthPatients: 0,
    lastMonthPatients: 0
  });
  const [recentPatients, setRecentPatients] = useState<TRQUser[]>([]);
  const [recentDoctors, setRecentDoctors] = useState<TRQUser[]>([]);
  const [monthlyStats, setMonthlyStats] = useState({
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    patients: [12, 19, 23, 28, 35, 42],
    questionnaires: [8, 15, 18, 22, 28, 31]
  });

  // Permission checks
  const canViewPatients = useCallback(() => {
    return checkPermission(RESOURCES.PATIENTS, ACTIONS.READ);
  }, [checkPermission, RESOURCES.PATIENTS, ACTIONS.READ]);

  const canViewDoctors = useCallback(() => {
    return checkPermission(RESOURCES.USERS, ACTIONS.READ);
  }, [checkPermission, RESOURCES.USERS, ACTIONS.READ]);

  const canManageUsers = useCallback(() => {
    return checkPermission(RESOURCES.USERS, ACTIONS.MANAGE);
  }, [checkPermission, RESOURCES.USERS, ACTIONS.MANAGE]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch current user data
        const userData = await getCurrentUserData();
        setCurrentUser(userData);

        // Fetch role counts and activity stats
        const [roleCountsData, systemStats] = await Promise.all([
          getUserRoleCounts(),
          getSystemActivityStats()
        ]);

        // Set stats
        setStats({
          totalPatients: roleCountsData.patientCount || 0,
          totalDoctors: roleCountsData.doctorCount || 0,
          totalClinics: roleCountsData.clinicCount || 1,
          activeQuestionnaires: systemStats.questionnaireCount || 0,
          pendingReports: Math.floor(Math.random() * 15) + 5, // Mock data
          completedReports: Math.floor(Math.random() * 50) + 25, // Mock data
          thisMonthPatients: Math.floor(Math.random() * 20) + 10,
          lastMonthPatients: Math.floor(Math.random() * 18) + 8
        });

        // Fetch recent patients and doctors if permissions allow
        if (canViewPatients()) {
          const patients = await getUsers({
            role: Role.Patient,
            isActive: true,
            limit: 5,
            orderByField: 'updatedAt',
            orderDirection: 'desc'
          });
          setRecentPatients(patients);
        }

        if (canViewDoctors()) {
          const doctors = await getUsers({
            role: Role.Doctor,
            isActive: true,
            limit: 5,
            orderByField: 'updatedAt',
            orderDirection: 'desc'
          });
          setRecentDoctors(doctors);
        }

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [role, canViewPatients, canViewDoctors]);

  // Chart options
  const chartOptions: ApexOptions = {
    chart: {
      type: 'area',
      height: 300,
      toolbar: { show: false }
    },
    dataLabels: { enabled: false },
    stroke: { curve: 'smooth', width: 2 },
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.6,
        opacityTo: 0.1
      }
    },
    colors: [theme.palette.primary.main, theme.palette.secondary.main],
    xaxis: { categories: monthlyStats.categories },
    yaxis: { title: { text: 'Count' } },
    legend: { position: 'top' }
  };

  // Loading state
  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>Clinic Admin Dashboard</Typography>
        <LinearProgress sx={{ mb: 2 }} />
        <Typography variant="body2">Loading dashboard data...</Typography>
      </Box>
    );
  }

  // Calculate growth percentage
  const patientGrowth = stats.lastMonthPatients > 0 
    ? ((stats.thisMonthPatients - stats.lastMonthPatients) / stats.lastMonthPatients * 100).toFixed(1)
    : '0';

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3, mb: 3, background: `linear-gradient(135deg, ${theme.palette.primary.main}20 0%, ${theme.palette.secondary.main}20 100%)` }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box>
                <Typography variant="h4" component="h1" gutterBottom>
                  Clinic Administration Dashboard
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  Welcome back, {currentUser?.firstName} {currentUser?.lastName}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Manage your clinic operations, monitor patient care, and track performance metrics
                </Typography>
              </Box>
              <Chip 
                icon={<BusinessIcon />} 
                label="Clinic Administrator" 
                color="primary" 
                size="medium"
                sx={{ fontSize: '0.875rem', px: 1 }}
              />
            </Box>
            
            {/* Growth Alert */}
            <Alert severity={parseFloat(patientGrowth) >= 0 ? "success" : "warning"} sx={{ mt: 2 }}>
              <AlertTitle>Monthly Performance</AlertTitle>
              Patient registrations {parseFloat(patientGrowth) >= 0 ? 'increased' : 'decreased'} by {Math.abs(parseFloat(patientGrowth))}% this month
            </Alert>
          </Paper>
        </Grid>

        {/* Navigation Tabs */}
        <Grid item xs={12}>
          <Paper sx={{ mb: 3 }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange} 
              variant="fullWidth"
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              <Tab label="Overview" icon={<DashboardIcon />} iconPosition="start" />
              <Tab label="Patient Management" icon={<PeopleIcon />} iconPosition="start" />
              <Tab label="Staff & Operations" icon={<LocalHospitalIcon />} iconPosition="start" />
              <Tab label="Reports & Analytics" icon={<ReportIcon />} iconPosition="start" />
            </Tabs>
          </Paper>
        </Grid>

        {/* Tab Content */}
        <Grid item xs={12}>
          {tabValue === 0 && (
            <Grid container spacing={3}>
              {/* Key Metrics Cards */}
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`, color: 'white' }}>
                  <CardContent sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h3" sx={{ fontWeight: 'bold' }}>{stats.totalPatients}</Typography>
                      <Typography variant="subtitle1">Total Patients</Typography>
                      <Typography variant="caption">+{stats.thisMonthPatients} this month</Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.3)', width: 56, height: 56 }}>
                      <PeopleIcon fontSize="large" />
                    </Avatar>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ background: `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.dark} 100%)`, color: 'white' }}>
                  <CardContent sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h3" sx={{ fontWeight: 'bold' }}>{stats.totalDoctors}</Typography>
                      <Typography variant="subtitle1">Medical Staff</Typography>
                      <Typography variant="caption">Active practitioners</Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.3)', width: 56, height: 56 }}>
                      <LocalHospitalIcon fontSize="large" />
                    </Avatar>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`, color: 'white' }}>
                  <CardContent sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h3" sx={{ fontWeight: 'bold' }}>{stats.activeQuestionnaires}</Typography>
                      <Typography variant="subtitle1">Active Assessments</Typography>
                      <Typography variant="caption">In progress</Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.3)', width: 56, height: 56 }}>
                      <AssignmentIcon fontSize="large" />
                    </Avatar>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`, color: 'white' }}>
                  <CardContent sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h3" sx={{ fontWeight: 'bold' }}>{stats.totalClinics}</Typography>
                      <Typography variant="subtitle1">Clinic Locations</Typography>
                      <Typography variant="caption">Under management</Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.3)', width: 56, height: 56 }}>
                      <BusinessIcon fontSize="large" />
                    </Avatar>
                  </CardContent>
                </Card>
              </Grid>

              {/* Charts */}
              <Grid item xs={12} md={8}>
                <Card>
                  <CardHeader 
                    title="Monthly Activity Trends" 
                    subheader="Patient registrations and questionnaire completions"
                    action={
                      <IconButton>
                        <MoreVertIcon />
                      </IconButton>
                    }
                  />
                  <Divider />
                  <CardContent>
                    <Chart 
                      options={chartOptions} 
                      series={[
                        { name: 'New Patients', data: monthlyStats.patients },
                        { name: 'Questionnaires', data: monthlyStats.questionnaires }
                      ]} 
                      type="area" 
                      height={300} 
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* Quick Actions */}
              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%' }}>
                  <CardHeader title="Quick Actions" />
                  <Divider />
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Button 
                          fullWidth 
                          variant="contained" 
                          startIcon={<AddIcon />}
                          onClick={() => navigate(ROUTES.PATIENTS.ADD)}
                          sx={{ mb: 1 }}
                        >
                          Add New Patient
                        </Button>
                      </Grid>
                      <Grid item xs={12}>
                        <Button 
                          fullWidth 
                          variant="outlined" 
                          startIcon={<LocalHospitalIcon />}
                          onClick={() => navigate(ROUTES.DOCTORS.ADD)}
                          sx={{ mb: 1 }}
                        >
                          Add Medical Staff
                        </Button>
                      </Grid>
                      <Grid item xs={12}>
                        <Button 
                          fullWidth 
                          variant="outlined" 
                          startIcon={<AssignmentIcon />}
                          onClick={() => navigate(ROUTES.QUESTIONNAIRES.LIST)}
                          sx={{ mb: 1 }}
                        >
                          Manage Questionnaires
                        </Button>
                      </Grid>
                      <Grid item xs={12}>
                        <Button 
                          fullWidth 
                          variant="outlined" 
                          startIcon={<SettingsIcon />}
                          onClick={() => navigate(ROUTES.OTHERS.SETTINGS)}
                        >
                          Clinic Settings
                        </Button>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Recent Activity */}
              <Grid item xs={12}>
                <Card>
                  <CardHeader 
                    title="Recent Activity" 
                    action={
                      <Button 
                        size="small" 
                        endIcon={<VisibilityIcon />}
                        onClick={() => navigate(ROUTES.PATIENTS.LIST)}
                      >
                        View All
                      </Button>
                    }
                  />
                  <Divider />
                  <List>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: theme.palette.success.light }}>
                          <CheckCircleIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText 
                        primary="New Patient Registration" 
                        secondary="John Smith completed registration and initial assessment - 2 hours ago"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: theme.palette.info.light }}>
                          <AssignmentIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText 
                        primary="Questionnaire Completed" 
                        secondary="Sarah Johnson finished respiratory health assessment - 4 hours ago"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: theme.palette.warning.light }}>
                          <WarningIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText 
                        primary="Review Required" 
                        secondary="Dr. Martinez needs to review Michael Brown's assessment - 6 hours ago"
                      />
                    </ListItem>
                  </List>
                </Card>
              </Grid>
            </Grid>
          )}

          {tabValue === 1 && (
            <Grid container spacing={3}>
              {/* Patient Management Content */}
              <Grid item xs={12} md={8}>
                <Card>
                  <CardHeader 
                    title="Recent Patients" 
                    action={
                      <Button variant="outlined" onClick={() => navigate(ROUTES.PATIENTS.LIST)}>
                        View All Patients
                      </Button>
                    }
                  />
                  <Divider />
                  <List>
                    {recentPatients.slice(0, 5).map((patient) => (
                      <ListItem 
                        key={patient.uid}
                        secondaryAction={
                          <IconButton onClick={() => navigate(ROUTES.PATIENTS.DETAILS(patient.uid))}>
                            <VisibilityIcon />
                          </IconButton>
                        }
                      >
                        <ListItemAvatar>
                          <Avatar>
                            {patient.firstName?.[0] || patient.email[0].toUpperCase()}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText 
                          primary={`${patient.firstName || ''} ${patient.lastName || ''}`}
                          secondary={`${patient.email} • Last login: ${patient.lastLogin ? new Date(patient.lastLogin).toLocaleDateString() : 'Never'}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card>
                  <CardHeader title="Patient Statistics" />
                  <Divider />
                  <CardContent>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">Active Patients</Typography>
                      <Typography variant="h4">{stats.totalPatients}</Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">New This Month</Typography>
                      <Typography variant="h4" color="primary.main">+{stats.thisMonthPatients}</Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">Growth Rate</Typography>
                      <Typography variant="h4" color={parseFloat(patientGrowth) >= 0 ? "success.main" : "warning.main"}>
                        {patientGrowth}%
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {tabValue === 2 && (
            <Grid container spacing={3}>
              {/* Staff Management Content */}
              <Grid item xs={12} md={8}>
                <Card>
                  <CardHeader 
                    title="Medical Staff" 
                    action={
                      <Button variant="outlined" onClick={() => navigate(ROUTES.DOCTORS.LIST)}>
                        View All Staff
                      </Button>
                    }
                  />
                  <Divider />
                  <List>
                    {recentDoctors.slice(0, 5).map((doctor) => (
                      <ListItem 
                        key={doctor.uid}
                        secondaryAction={
                          <IconButton onClick={() => navigate(ROUTES.DOCTORS.DETAILS(doctor.uid))}>
                            <VisibilityIcon />
                          </IconButton>
                        }
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: theme.palette.secondary.main }}>
                            <LocalHospitalIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText 
                          primary={`Dr. ${doctor.firstName || ''} ${doctor.lastName || ''}`}
                          secondary={`${doctor.email} • ${doctor.speciality || 'General Practice'}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card>
                  <CardHeader title="Staff Overview" />
                  <Divider />
                  <CardContent>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">Total Doctors</Typography>
                      <Typography variant="h4">{stats.totalDoctors}</Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">Active Today</Typography>
                      <Typography variant="h4" color="success.main">{Math.floor(stats.totalDoctors * 0.8)}</Typography>
                    </Box>
                    <Button 
                      fullWidth 
                      variant="contained" 
                      startIcon={<AddIcon />}
                      onClick={() => navigate(ROUTES.DOCTORS.ADD)}
                    >
                      Add New Doctor
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {tabValue === 3 && (
            <Grid container spacing={3}>
              {/* Reports & Analytics Content */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader title="Report Status" />
                  <Divider />
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body1">Pending Reviews</Typography>
                      <Chip label={stats.pendingReports} color="warning" size="small" />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body1">Completed Reports</Typography>
                      <Chip label={stats.completedReports} color="success" size="small" />
                    </Box>
                    <Button 
                      fullWidth 
                      variant="outlined" 
                      startIcon={<ReportIcon />}
                      onClick={() => navigate(ROUTES.COMPLIANCE_REPORTS.LIST)}
                    >
                      View All Reports
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader title="System Analytics" />
                  <Divider />
                  <CardContent>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">System Usage</Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={75} 
                        sx={{ mt: 1, height: 8, borderRadius: 4 }}
                      />
                      <Typography variant="caption" color="text.secondary">75% capacity</Typography>
                    </Box>
                    <Button 
                      fullWidth 
                      variant="outlined" 
                      startIcon={<TrendingUpIcon />}
                      onClick={() => navigate(ROUTES.ANALYTICS.BASE)}
                    >
                      View Analytics
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default ClinicAdminHomePage;

import React from 'react';
import { Chip } from '@mui/material';
import { PaymentStatus } from '../[types]/Purchase';

// icons
import PendingIcon from '@mui/icons-material/Pending';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import ReceiptIcon from '@mui/icons-material/Receipt';

export const getStatusChip = (status: string) => {
  const statusConfig = {
    [PaymentStatus.Pending]: {
      icon: <PendingIcon fontSize="small" />,
      label: 'Pending',
      color: 'warning'
    },
    [PaymentStatus.Completed]: {
      icon: <CheckCircleIcon fontSize="small" />,
      label: 'Completed',
      color: 'success'
    },
    [PaymentStatus.Failed]: {
      icon: <CreditCardIcon fontSize="small" />,
      label: 'Failed',
      color: 'error'
    },
    [PaymentStatus.Refunded]: {
      icon: <ReceiptIcon fontSize="small" />,
      label: 'Refunded',
      color: 'info'
    }
  };

  const config = statusConfig[status as PaymentStatus] || statusConfig[PaymentStatus.Pending];

  return (
    <Chip
      icon={config.icon}
      label={config.label}
      color={config.color as any}
      size="small"
      variant="outlined"
      sx={{
        '& .MuiChip-icon': { fontSize: '1rem' }
      }}
    />
  );
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

export const formatDate = (date: any): string => {
  const dateObj = date?.toDate ? date.toDate() : new Date(date);
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

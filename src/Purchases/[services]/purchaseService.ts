// Purchase service
// Handles operations for purchases and billing

import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  addDoc,
  updateDoc,
  Timestamp,
  DocumentData,
  QueryDocumentSnapshot
} from 'firebase/firestore';
import { db as firestore } from '../../Firebase/[config]/firebase';
import { Purchase, PaymentStatus } from '../[types]/Purchase'; // Corrected path

// Collection reference using modular Firestore SDK
const purchasesCollection = collection(firestore, 'purchases');

/**
 * Convert Firestore document to Purchase
 */
export const convertToPurchase = (doc: QueryDocumentSnapshot<DocumentData>): Purchase => {
  const data = doc.data();

  return {
    id: doc.id,
    clientId: data.clientId || '',
    clientName: data.clientName || '',
    questionnaires: data.questionnaires || [],
    totalQuantity: data.totalQuantity || 0,
    totalPrice: data.totalPrice || 0,
    paymentStatus: data.paymentStatus || PaymentStatus.Pending,
    purchaseDate: data.purchaseDate || Timestamp.now(),
    lastUpdated: data.lastUpdated || Timestamp.now(),
    paymentMethod: data.paymentMethod || '',
    transactionId: data.transactionId || '',
    notes: data.notes || '',
    templateId: data.templateId || '',
    questionnaireQuantity: data.questionnaireQuantity || 0,
    unitPrice: data.unitPrice || 0,
    subTotal: data.subTotal || 0,
    tax: data.tax || 0,
    isPaid: data.isPaid || false,
    date: data.date || data.purchaseDate || Timestamp.now(),
    note: data.note || '',
    paymentType: data.paymentType || 'card',
    billingInfo: data.billingInfo || undefined
  };
};

/**
 * Get a purchase by ID
 */
export const getPurchaseById = async (id: string): Promise<Purchase | null> => {
  try {
    const purchaseDoc = await getDoc(doc(firestore, 'purchases', id));
    if (!purchaseDoc.exists()) {
      return null;
    }
    return convertToPurchase(purchaseDoc as any);
  } catch (error) {
    console.error('Error getting purchase by ID:', error);
    throw error;
  }
};

/**
 * Get purchases for a client
 */
export const getPurchasesByClient = async (
  clientId: string,
  options: {
    status?: string;
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<Purchase[]> => {
  try {
    console.log('getPurchasesByClient called with clientId:', clientId, 'options:', options);
    let q;

    // Handle special case for all clients
    if (clientId === 'all-clients') {
      console.log('Fetching all purchases (admin mode)');
      q = query(purchasesCollection);
    } else {
      console.log('Fetching purchases for specific client:', clientId);
      q = query(purchasesCollection, where('clientId', '==', clientId));
    }

    // Apply filters
    if (options.status) {
      console.log('Applying status filter:', options.status);
      q = query(q, where('paymentStatus', '==', options.status));
    }

    // Apply sorting
    if (options.orderByField) {
      console.log('Applying custom sorting:', options.orderByField, options.orderDirection);
      q = query(q, orderBy(options.orderByField, options.orderDirection || 'desc'));
    } else {
      console.log('Applying default sorting by purchaseDate desc');
      q = query(q, orderBy('purchaseDate', 'desc'));
    }

    // Apply limit
    if (options.limit) {
      console.log('Applying limit:', options.limit);
      q = query(q, limit(options.limit));
    }

    console.log('Executing Firestore query...');
    const querySnapshot = await getDocs(q);
    const purchases = querySnapshot.docs.map((doc) => convertToPurchase(doc));
    console.log('Found', purchases.length, 'purchases for client:', clientId);
    console.log('Purchase details:', purchases.map(p => ({ id: p.id, clientId: p.clientId, totalPrice: p.totalPrice })));
    return purchases;
  } catch (error) {
    console.error('Error getting purchases for client', clientId, ':', error);
    return [];
  }
};

/**
 * Create a new purchase
 */
export const createPurchase = async (data: Omit<Purchase, 'id'>): Promise<Purchase> => {
  try {
    const now = Timestamp.now();
    const purchaseData = {
      ...data,
      purchaseDate: data.purchaseDate || now,
      lastUpdated: now,
      date: data.date || data.purchaseDate || now
    };

    const docRef = await addDoc(purchasesCollection, purchaseData);
    const newPurchase = await getDoc(docRef);

    if (!newPurchase.exists()) {
      throw new Error('Failed to create purchase');
    }

    return convertToPurchase(newPurchase as any);
  } catch (error) {
    console.error('Error creating purchase:', error);
    throw error;
  }
};

/**
 * Update an existing purchase
 */
export const updatePurchase = async (id: string, data: Partial<Purchase>): Promise<Purchase> => {
  try {
    const purchaseRef = doc(firestore, 'purchases', id);
    const purchaseDoc = await getDoc(purchaseRef);

    if (!purchaseDoc.exists()) {
      throw new Error(`Purchase with ID ${id} not found`);
    }

    const now = Timestamp.now();
    // Remove id property to avoid updating the document ID
    const dataWithoutId = { ...data };
    delete dataWithoutId.id;

    await updateDoc(purchaseRef, {
      ...dataWithoutId,
      updatedAt: now
    });

    const updatedPurchase = await getDoc(purchaseRef);
    return convertToPurchase(updatedPurchase as any);
  } catch (error) {
    console.error('Error updating purchase:', error);
    throw error;
  }
};

/**
 * Complete a purchase
 */
export const completePurchase = async (id: string, transactionId: string, paymentMethod: string): Promise<Purchase> => {
  try {
    const purchaseRef = doc(firestore, 'purchases', id);
    const purchaseDoc = await getDoc(purchaseRef);

    if (!purchaseDoc.exists()) {
      throw new Error(`Purchase with ID ${id} not found`);
    }

    const now = Timestamp.now();

    await updateDoc(purchaseRef, {
      status: 'completed',
      completedAt: now,
      transactionId,
      paymentMethod,
      updatedAt: now
    });

    const updatedPurchase = await getDoc(purchaseRef);
    return convertToPurchase(updatedPurchase as any);
  } catch (error) {
    console.error('Error completing purchase:', error);
    throw error;
  }
};

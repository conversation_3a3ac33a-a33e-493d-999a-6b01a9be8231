import { useState, useCallback } from 'react';

interface UsePurchaseFiltersReturn {
  searchTerm: string;
  statusFilter: string;
  expandedRows: Set<string>;
  handleSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleStatusFilterChange: (event: any) => void;
  toggleRowExpansion: (purchaseId: string) => void;
  clearFilters: () => void;
}

export const usePurchaseFilters = (): UsePurchaseFiltersReturn => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  }, []);

  const handleStatusFilterChange = useCallback((event: any) => {
    setStatusFilter(event.target.value);
  }, []);

  const toggleRowExpansion = useCallback((purchaseId: string) => {
    setExpandedRows(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(purchaseId)) {
        newExpanded.delete(purchaseId);
      } else {
        newExpanded.add(purchaseId);
      }
      return newExpanded;
    });
  }, []);

  const clearFilters = useCallback(() => {
    setSearchTerm('');
    setStatusFilter('all');
    setExpandedRows(new Set());
  }, []);

  return {
    searchTerm,
    statusFilter,
    expandedRows,
    handleSearchChange,
    handleStatusFilterChange,
    toggleRowExpansion,
    clearFilters
  };
};

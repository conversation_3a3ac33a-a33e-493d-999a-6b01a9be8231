import { useState, useEffect, useMemo } from 'react';
import { useAuth } from 'Authentication/[contexts]/AuthContext';
import { Role } from 'RBAC/[types]/Role';
import { Purchase, PaymentStatus } from '../[types]/Purchase';
import { getPurchasesByClient } from '../[services]/purchaseService';

interface UsePurchasesOptions {
  searchTerm?: string;
  statusFilter?: string;
}

interface UsePurchasesReturn {
  purchases: Purchase[];
  filteredPurchases: Purchase[];
  loading: boolean;
  error: string | null;
  isClientUser: boolean;
  stats: {
    totalPurchases: number;
    totalAmount: number;
    completedPurchases: number;
    pendingPurchases: number;
    questionnaireBreakdown: Record<string, number>;
  };
  refetch: () => Promise<void>;
}

export const usePurchases = (options: UsePurchasesOptions = {}): UsePurchasesReturn => {
  const { userData } = useAuth();
  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const { searchTerm = '', statusFilter = 'all' } = options;

  const isClientUser = userData?.role === Role.Client;

  const fetchPurchases = async () => {
    if (!userData?.uid) {
      console.log('No user data available');
      setPurchases([]);
      setLoading(false);
      return;
    }

    if (!isClientUser) {
      console.log('User is not a client, role:', userData.role);
      setPurchases([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      console.log('Fetching purchases for client:', userData.uid);
      const purchasesData = await getPurchasesByClient(userData.uid);
      console.log('Fetched purchases:', purchasesData);
      setPurchases(purchasesData || []);
    } catch (err) {
      console.error('Error fetching purchases:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch purchases');
      setPurchases([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPurchases();
  }, [userData?.uid, userData?.role]);

  // Memoized filtered purchases
  const filteredPurchases = useMemo(() => {
    return purchases.filter((purchase) => {
      const matchesSearch = purchase.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           purchase.clientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           purchase.questionnaires?.some(q => 
                             q.name.toLowerCase().includes(searchTerm.toLowerCase())
                           );
      const matchesStatus = statusFilter === 'all' || purchase.paymentStatus === statusFilter;
      return matchesSearch && matchesStatus;
    });
  }, [purchases, searchTerm, statusFilter]);

  // Memoized statistics
  const stats = useMemo(() => {
    const totalAmount = filteredPurchases.reduce((sum, purchase) => sum + purchase.totalPrice, 0);
    const completedPurchases = filteredPurchases.filter((p) => p.paymentStatus === PaymentStatus.Completed).length;
    const pendingPurchases = filteredPurchases.filter((p) => p.paymentStatus === PaymentStatus.Pending).length;
    
    const questionnaireBreakdown = filteredPurchases.reduce((acc, purchase) => {
      purchase.questionnaires?.forEach(q => {
        const key = q.name;
        if (acc[key]) {
          acc[key] += q.quantity;
        } else {
          acc[key] = q.quantity;
        }
      });
      return acc;
    }, {} as Record<string, number>);

    return {
      totalPurchases: filteredPurchases.length,
      totalAmount,
      completedPurchases,
      pendingPurchases,
      questionnaireBreakdown
    };
  }, [filteredPurchases]);

  return {
    purchases,
    filteredPurchases,
    loading,
    error,
    isClientUser,
    stats,
    refetch: fetchPurchases
  };
};

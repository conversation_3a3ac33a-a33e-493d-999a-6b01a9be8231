import React from 'react';
import { Grid, Paper, Box, Typography, Chip, useTheme } from '@mui/material';
import { useIntl } from 'react-intl';
import AssignmentIcon from '@mui/icons-material/Assignment';

interface QuestionnaireBreakdownProps {
  questionnaireBreakdown: Record<string, number>;
}

const QuestionnaireBreakdown: React.FC<QuestionnaireBreakdownProps> = React.memo(({
  questionnaireBreakdown
}) => {
  const intl = useIntl();
  const theme = useTheme();

  if (Object.keys(questionnaireBreakdown).length === 0) {
    return (
      <Grid item xs={12}>
        <Paper
          elevation={3}
          sx={{
            p: 3,
            borderRadius: 2,
            mb: 2
          }}
        >
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
            {intl.formatMessage({ id: 'questionnaire-breakdown' }) || 'Questionnaire Breakdown'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            No questionnaires purchased yet.
          </Typography>
        </Paper>
      </Grid>
    );
  }

  return (
    <Grid item xs={12}>
      <Paper
        elevation={3}
        sx={{
          p: 3,
          borderRadius: 2,
          mb: 2
        }}
      >
        <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
          {intl.formatMessage({ id: 'questionnaire-breakdown' }) || 'Questionnaire Breakdown'}
        </Typography>
        <Grid container spacing={2}>
          {Object.entries(questionnaireBreakdown).map(([name, quantity]) => (
            <Grid item xs={12} sm={6} md={4} key={name}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  p: 2,
                  bgcolor: 'background.default',
                  borderRadius: 1,
                  border: `1px solid ${theme.palette.divider}`,
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    bgcolor: 'action.hover',
                    transform: 'translateY(-2px)',
                    boxShadow: theme.shadows[4]
                  }
                }}
              >
                <AssignmentIcon sx={{ color: theme.palette.primary.main, mr: 2 }} />
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="body1" fontWeight="medium" noWrap>
                    {name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {quantity} purchased
                  </Typography>
                </Box>
                <Chip
                  label={quantity}
                  color="primary"
                  size="small"
                  sx={{ fontWeight: 600 }}
                />
              </Box>
            </Grid>
          ))}
        </Grid>
      </Paper>
    </Grid>
  );
};

});

export default QuestionnaireBreakdown;

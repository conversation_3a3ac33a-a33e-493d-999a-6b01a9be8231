import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Box,
  Typography,
  IconButton,
  Tooltip,
  Stack,
  Chip,
  useTheme
} from '@mui/material';
import { 
  DataGrid, 
  GridColDef, 
  GridRenderCellParams, 
  GridToolbarContainer 
} from '@mui/x-data-grid';

// icons
import VisibilityTwoToneIcon from '@mui/icons-material/VisibilityTwoTone';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import AssignmentIcon from '@mui/icons-material/Assignment';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

import { Purchase } from '../[types]/Purchase';
import { getStatusChip, formatCurrency, formatDate } from '../[utils]/purchaseUtils';
import useDataGrid from '[hooks]/useDataGrid';

interface PurchaseDataGridProps {
  purchases: Purchase[];
  loading: boolean;
  expandedRows: Set<string>;
  onToggleRowExpansion: (purchaseId: string) => void;
  onRowClick?: (purchaseId: string) => void;
}

interface CustomToolbarProps {
  onNewPurchase: () => void;
}

const CustomToolbar: React.FC<CustomToolbarProps> = ({ onNewPurchase }) => {
  const intl = useIntl();
  
  return (
    <GridToolbarContainer>
      <Box display="flex" justifyContent="space-between" width="100%" alignItems="center" p={1.5}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          {intl.formatMessage({ id: 'my-purchases' }) || 'Purchase History'}
        </Typography>
      </Box>
    </GridToolbarContainer>
  );
};

const PurchaseDataGrid: React.FC<PurchaseDataGridProps> = React.memo(({
  purchases,
  loading,
  expandedRows,
  onToggleRowExpansion,
  onRowClick
}) => {
  const intl = useIntl();
  const navigate = useNavigate();
  const theme = useTheme();
  const dataGridStyles = useDataGrid();

  const handleViewDetails = (id: string) => {
    navigate(`/trq/purchases/${id}`);
  };

  const handleRowClick = (purchaseId: string) => {
    if (onRowClick) {
      onRowClick(purchaseId);
    } else {
      navigate(`/trq/purchases/${purchaseId}`);
    }
  };

  const columns: GridColDef[] = useMemo(() => [
    {
      field: 'purchaseDate',
      headerName: intl.formatMessage({ id: 'purchase-date' }) || 'Purchase Date',
      flex: 0.8,
      width: 200,
      renderCell: (params: GridRenderCellParams<Purchase>) => {
        return (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CalendarTodayIcon sx={{ color: theme.palette.primary.main, mr: 1, fontSize: '1rem' }} />
            <Typography variant="body2">
              {formatDate(params.row.purchaseDate)}
            </Typography>
          </Box>
        );
      }
    },
    {
      field: 'questionnaires',
      headerName: intl.formatMessage({ id: 'questionnaires' }) || 'Questionnaires',
      flex: 1.5,
      renderCell: (params: GridRenderCellParams<Purchase>) => {
        const questionnaires = params.row.questionnaires || [];
        const purchaseId = params.row.id;
        const isExpanded = expandedRows.has(purchaseId);
        
        const toggleExpanded = (event: React.MouseEvent) => {
          event.stopPropagation();
          onToggleRowExpansion(purchaseId);
        };

        if (questionnaires.length === 0) {
          return (
            <Typography variant="body2" color="text.secondary">
              No questionnaires
            </Typography>
          );
        }

        const displayedQuestionnaires = isExpanded ? questionnaires : questionnaires.slice(0, 1);
        const hasMore = questionnaires.length > 1;

        return (
          <Box sx={{ py: 1, width: '100%' }}>
            {displayedQuestionnaires.map((q, index) => (
              <Box key={q.id || index} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <AssignmentIcon sx={{ color: theme.palette.primary.main, mr: 1, fontSize: '0.9rem' }} />
                <Typography variant="body2" sx={{ mr: 1, flexGrow: 1 }} noWrap>
                  {q.name}
                </Typography>
                <Chip
                  label={`×${q.quantity}`}
                  size="small"
                  variant="outlined"
                  color="primary"
                  sx={{ 
                    height: '20px',
                    fontSize: '0.7rem',
                    '& .MuiChip-label': { px: 1 }
                  }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                  {formatCurrency(q.unitPrice || 0)}
                </Typography>
              </Box>
            ))}
            
            {hasMore && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                <IconButton 
                  size="small" 
                  onClick={toggleExpanded}
                  sx={{ p: 0.5 }}
                >
                  {isExpanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
                </IconButton>
                <Typography variant="caption" color="text.secondary" sx={{ ml: 0.5 }}>
                  {isExpanded 
                    ? 'Show less' 
                    : `+${questionnaires.length - 1} more`
                  }
                </Typography>
              </Box>
            )}
          </Box>
        );
      }
    },
    {
      field: 'totalQuantity',
      headerName: intl.formatMessage({ id: 'total-quantity' }) || 'Total Qty',
      flex: 0.5,
      align: 'center',
      renderCell: (params: GridRenderCellParams<Purchase>) => (
        <Typography variant="body2" fontWeight="medium">
          {params.row.totalQuantity}
        </Typography>
      )
    },
    {
      field: 'totalPrice',
      headerName: intl.formatMessage({ id: 'total-price' }) || 'Amount',
      flex: 0.8,
      align: 'right',
      renderCell: (params: GridRenderCellParams<Purchase>) => (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
          <AttachMoneyIcon sx={{ color: theme.palette.success.main, mr: 0.5, fontSize: '1.1rem' }} />
          <Typography variant="body2" fontWeight="medium">
            {formatCurrency(params.row.totalPrice)}
          </Typography>
        </Box>
      )
    },
    {
      field: 'paymentStatus',
      headerName: intl.formatMessage({ id: 'payment-status' }) || 'Status',
      flex: 0.8,
      align: 'center',
      headerAlign: 'center',
      renderCell: (params: GridRenderCellParams<Purchase>) => {
        return (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%' }}>
            {getStatusChip(params.row.paymentStatus)}
          </Box>
        );
      }
    },
    {
      field: 'actions',
      headerName: intl.formatMessage({ id: 'actions' }) || 'Actions',
      flex: 0.6,
      sortable: false,
      align: 'center',
      renderCell: (params: GridRenderCellParams<Purchase>) => (
        <Stack direction="row" spacing={1}>
          <Tooltip title="View Details">
            <IconButton
              color="primary"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleViewDetails(params.row.id);
              }}
            >
              <VisibilityTwoToneIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Stack>
      )
    }
  ], [intl, theme, expandedRows, onToggleRowExpansion]);

  return (
    <DataGrid
      rows={purchases}
      columns={columns}
      loading={loading}
      onRowClick={(params) => handleRowClick(params.row.id)}
      sx={{
        ...dataGridStyles,
        '& .MuiDataGrid-row': {
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: theme.palette.action.hover
          }
        },
        '& .MuiDataGrid-cell': {
          padding: '16px',
          '&:focus': {
            outline: 'none'
          }
        },
        border: 'none',
        borderRadius: 2
      }}
      slots={{
        toolbar: () => <CustomToolbar onNewPurchase={() => navigate('/trq/products')} />
      }}
      pageSizeOptions={[10, 25, 50, 100]}
      initialState={{
        pagination: {
          paginationModel: { pageSize: 10, page: 0 }
        }
      }}
    />
  );
};

});

export default PurchaseDataGrid;

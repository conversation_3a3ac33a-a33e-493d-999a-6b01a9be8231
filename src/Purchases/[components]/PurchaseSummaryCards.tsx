import React from 'react';
import { Grid, Paper, Box, Avatar, Typography, useTheme } from '@mui/material';
import { useIntl } from 'react-intl';

// icons
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';

interface PurchaseSummaryCardsProps {
  stats: {
    totalPurchases: number;
    totalAmount: number;
    completedPurchases: number;
    pendingPurchases: number;
  };
}

interface SummaryCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
}

const SummaryCard: React.FC<SummaryCardProps> = ({ title, value, icon, color }) => {
  const theme = useTheme();
  
  return (
    <Paper
      elevation={3}
      sx={{
        p: 2.5,
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          width: '100%',
          height: '5px',
          top: 0,
          left: 0,
          backgroundColor: color
        }
      }}
    >
      <Box sx={{ position: 'relative', zIndex: 1 }}>
        <Avatar
          sx={{
            width: 48,
            height: 48,
            bgcolor: `${color}20`,
            color: color,
            mb: 2
          }}
        >
          {icon}
        </Avatar>
        <Typography variant="h4" fontWeight="medium" gutterBottom>
          {value}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          {title}
        </Typography>
      </Box>
    </Paper>
  );
};

const PurchaseSummaryCards: React.FC<PurchaseSummaryCardsProps> = React.memo(({ stats }) => {
  const intl = useIntl();
  const theme = useTheme();

  const cards = [
    {
      title: intl.formatMessage({ id: 'total-purchases' }) || 'Total Purchases',
      value: stats.totalPurchases,
      icon: <ShoppingCartIcon />,
      color: theme.palette.primary.main
    },
    {
      title: intl.formatMessage({ id: 'total-amount' }) || 'Total Amount',
      value: new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(stats.totalAmount),
      icon: <AttachMoneyIcon />,
      color: theme.palette.success.main
    },
    {
      title: intl.formatMessage({ id: 'completed-purchases' }) || 'Completed Purchases',
      value: stats.completedPurchases,
      icon: <CheckCircleIcon />,
      color: theme.palette.success.main
    },
    {
      title: intl.formatMessage({ id: 'pending-purchases' }) || 'Pending Purchases',
      value: stats.pendingPurchases,
      icon: <PendingIcon />,
      color: theme.palette.warning.main
    }
  ];

  return (
    <>
      {cards.map((card, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <SummaryCard {...card} />
        </Grid>
      ))}
    </>
  );
};

});

export default PurchaseSummaryCards;

import React from 'react';
import { Grid, Paper, Typography, Button, Box } from '@mui/material';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import BlockIcon from '@mui/icons-material/Block';
import { Role } from 'RBAC/[types]/Role';

interface PurchaseEmptyStateProps {
  isClientUser: boolean;
  userRole?: Role;
  hasFilters: boolean;
  searchTerm?: string;
  statusFilter?: string;
}

const PurchaseEmptyState: React.FC<PurchaseEmptyStateProps> = React.memo(({
  isClientUser,
  userRole,
  hasFilters,
  searchTerm,
  statusFilter
}) => {
  const intl = useIntl();
  const navigate = useNavigate();

  const getEmptyStateContent = () => {
    if (!isClientUser) {
      return {
        icon: <BlockIcon sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />,
        title: 'Access Restricted',
        description: 'This page is only available to client users. Please contact your administrator if you believe this is an error.',
        showButton: false
      };
    }

    if (hasFilters) {
      return {
        icon: <ShoppingCartIcon sx={{ fontSize: 60, color: 'action.disabled', mb: 2 }} />,
        title: intl.formatMessage({ id: 'no-purchases-found' }) || 'No Purchases Found',
        description: intl.formatMessage({ id: 'no-matching-purchases' }) || 'No purchases match your search criteria.',
        showButton: false
      };
    }

    return {
      icon: <ShoppingCartIcon sx={{ fontSize: 60, color: 'action.disabled', mb: 2 }} />,
      title: intl.formatMessage({ id: 'no-purchases-found' }) || 'No Purchases Found',
      description: intl.formatMessage({ id: 'no-purchases-yet' }) || "You haven't made any purchases yet.",
      showButton: true
    };
  };

  const { icon, title, description, showButton } = getEmptyStateContent();

  return (
    <Grid item xs={12}>
      <Paper
        elevation={3}
        sx={{
          p: 4,
          textAlign: 'center',
          borderRadius: 2,
          bgcolor: 'background.paper'
        }}
      >
        {icon}
        <Typography variant="h5" gutterBottom>
          {title}
        </Typography>
        <Typography variant="body1" color="textSecondary" paragraph>
          {description}
        </Typography>
        
        {showButton && isClientUser && (
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<ShoppingCartIcon />}
              onClick={() => navigate('/trq/products')}
              sx={{ 
                mt: 2, 
                borderRadius: 2,
                px: 3,
                py: 1.5
              }}
            >
              {intl.formatMessage({ id: 'browse-products' }) || 'Browse Products'}
            </Button>
          </Box>
        )}

        {hasFilters && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Try adjusting your search criteria or clearing the filters.
            </Typography>
          </Box>
        )}
      </Paper>
    </Grid>
  );
};

});

export default PurchaseEmptyState;

import React from 'react';
import { 
  Grid, 
  Paper, 
  Stack, 
  TextField, 
  MenuItem, 
  Typography, 
  Chip,
  Button,
  Box
} from '@mui/material';
import { useIntl } from 'react-intl';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import { PaymentStatus } from '../[types]/Purchase';

interface PurchaseFiltersProps {
  searchTerm: string;
  statusFilter: string;
  onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onStatusFilterChange: (event: any) => void;
  onClearFilters: () => void;
  getStatusChip: (status: string) => React.ReactNode;
}

const PurchaseFilters: React.FC<PurchaseFiltersProps> = React.memo(({
  searchTerm,
  statusFilter,
  onSearchChange,
  onStatusFilterChange,
  onClearFilters,
  getStatusChip
}) => {
  const intl = useIntl();

  const hasActiveFilters = searchTerm !== '' || statusFilter !== 'all';

  return (
    <Grid item xs={12}>
      <Paper
        elevation={3}
        sx={{
          p: 2,
          borderRadius: 2,
          mb: 2
        }}
      >
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          alignItems={{ xs: 'flex-start', sm: 'center' }}
          justifyContent="space-between"
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h5" sx={{ mb: { xs: 1, sm: 0 } }}>
              {intl.formatMessage({ id: 'filter-purchases' }) || 'Filter Purchases'}
            </Typography>
            {hasActiveFilters && (
              <Button
                size="small"
                startIcon={<ClearIcon />}
                onClick={onClearFilters}
                sx={{ 
                  textTransform: 'none',
                  color: 'text.secondary',
                  '&:hover': {
                    bgcolor: 'action.hover'
                  }
                }}
              >
                Clear Filters
              </Button>
            )}
          </Box>
          
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} width={{ xs: '100%', sm: 'auto' }}>
            <TextField
              size="small"
              placeholder={intl.formatMessage({ id: 'search-purchases' }) || 'Search purchases...'}
              value={searchTerm}
              onChange={onSearchChange}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />,
                sx: { borderRadius: 2 }
              }}
              sx={{ minWidth: { xs: '100%', sm: 220 } }}
            />
            <TextField
              select
              size="small"
              value={statusFilter}
              onChange={onStatusFilterChange}
              label="Status"
              InputProps={{
                sx: { borderRadius: 2 }
              }}
              sx={{ minWidth: { xs: '100%', sm: 150 } }}
            >
              <MenuItem value="all">
                {intl.formatMessage({ id: 'all-statuses' }) || 'All Statuses'}
              </MenuItem>
              {Object.values(PaymentStatus).map((status) => (
                <MenuItem key={status} value={status}>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    {getStatusChip(status)}
                  </Stack>
                </MenuItem>
              ))}
            </TextField>
          </Stack>
        </Stack>

        {hasActiveFilters && (
          <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {searchTerm && (
              <Chip
                label={`Search: "${searchTerm}"`}
                size="small"
                onDelete={() => onSearchChange({ target: { value: '' } } as any)}
                color="primary"
                variant="outlined"
              />
            )}
            {statusFilter !== 'all' && (
              <Chip
                label={`Status: ${statusFilter}`}
                size="small"
                onDelete={() => onStatusFilterChange({ target: { value: 'all' } })}
                color="primary"
                variant="outlined"
              />
            )}
          </Box>
        )}
      </Paper>
    </Grid>
  );
};

});

export default PurchaseFilters;

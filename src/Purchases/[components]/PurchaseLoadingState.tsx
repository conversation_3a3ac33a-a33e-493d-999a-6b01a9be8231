import React from 'react';
import { Grid, Paper, Skeleton, Box } from '@mui/material';

const PurchaseLoadingState: React.FC = () => {
  return (
    <>
      {/* Summary Cards Loading */}
      {[...Array(4)].map((_, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <Paper
            elevation={3}
            sx={{
              p: 2.5,
              borderRadius: 2,
              height: '140px'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Skeleton variant="circular" width={48} height={48} />
            </Box>
            <Skeleton variant="text" width="60%" height={32} sx={{ mb: 1 }} />
            <Skeleton variant="text" width="80%" height={20} />
          </Paper>
        </Grid>
      ))}

      {/* Questionnaire Breakdown Loading */}
      <Grid item xs={12}>
        <Paper
          elevation={3}
          sx={{
            p: 3,
            borderRadius: 2,
            mb: 2
          }}
        >
          <Skeleton variant="text" width="300px" height={32} sx={{ mb: 2 }} />
          <Grid container spacing={2}>
            {[...Array(6)].map((_, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    bgcolor: 'background.default',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'divider'
                  }}
                >
                  <Skeleton variant="circular" width={24} height={24} sx={{ mr: 2 }} />
                  <Box sx={{ flexGrow: 1 }}>
                    <Skeleton variant="text" width="80%" height={20} sx={{ mb: 0.5 }} />
                    <Skeleton variant="text" width="60%" height={16} />
                  </Box>
                  <Skeleton variant="rectangular" width={40} height={24} sx={{ borderRadius: 1 }} />
                </Box>
              </Grid>
            ))}
          </Grid>
        </Paper>
      </Grid>

      {/* Filters Loading */}
      <Grid item xs={12}>
        <Paper
          elevation={3}
          sx={{
            p: 2,
            borderRadius: 2,
            mb: 2
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Skeleton variant="text" width="200px" height={32} />
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Skeleton variant="rectangular" width={220} height={40} sx={{ borderRadius: 1 }} />
              <Skeleton variant="rectangular" width={150} height={40} sx={{ borderRadius: 1 }} />
            </Box>
          </Box>
        </Paper>
      </Grid>

      {/* Data Grid Loading */}
      <Grid item xs={12}>
        <Paper
          elevation={3}
          sx={{
            borderRadius: 2,
            overflow: 'hidden',
            height: 'calc(100vh - 460px)',
            minHeight: 400
          }}
        >
          {/* Toolbar */}
          <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
            <Skeleton variant="text" width="200px" height={32} />
          </Box>
          
          {/* Table Headers */}
          <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Skeleton variant="text" width="120px" height={20} />
              <Skeleton variant="text" width="200px" height={20} />
              <Skeleton variant="text" width="80px" height={20} />
              <Skeleton variant="text" width="100px" height={20} />
              <Skeleton variant="text" width="100px" height={20} />
              <Skeleton variant="text" width="80px" height={20} />
            </Box>
          </Box>
          
          {/* Table Rows */}
          {[...Array(8)].map((_, index) => (
            <Box key={index} sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <Skeleton variant="text" width="120px" height={20} />
                <Skeleton variant="text" width="200px" height={20} />
                <Skeleton variant="text" width="80px" height={20} />
                <Skeleton variant="text" width="100px" height={20} />
                <Skeleton variant="rectangular" width={80} height={24} sx={{ borderRadius: 1 }} />
                <Skeleton variant="circular" width={32} height={32} />
              </Box>
            </Box>
          ))}
        </Paper>
      </Grid>
    </>
  );
};

export default PurchaseLoadingState;

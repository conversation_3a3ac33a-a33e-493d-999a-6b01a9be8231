import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';

// material-ui
import {
  Box,
  Button,
  Grid,
  Stack,
  TextField,
  useTheme,
  MenuItem,
  Typography,
  Chip,
  Paper,
  IconButton,
  Tooltip,
  Avatar,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import { DataGrid, GridColDef, GridRenderCellParams, GridToolbarContainer } from '@mui/x-data-grid';

// project imports
import MainCard from '[components]/cards/MainCard';
import useDataGrid from '[hooks]/useDataGrid';
import { Purchase, PaymentStatus } from '../[types]/Purchase';

// icons
import VisibilityTwoToneIcon from '@mui/icons-material/VisibilityTwoTone';
import SearchIcon from '@mui/icons-material/Search';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import ReceiptIcon from '@mui/icons-material/Receipt';
import PendingIcon from '@mui/icons-material/Pending';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import AssignmentIcon from '@mui/icons-material/Assignment';

// services
import { getPurchasesByClient } from '../[services]/purchaseService';

// auth context
import { useAuth } from 'Authentication/[contexts]/AuthContext';

// role context
import { Role } from 'RBAC/[types]/Role';

const MyPurchases = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const theme = useTheme();
  const dataGridStyles = useDataGrid();
  const { userData } = useAuth();

  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  useEffect(() => {
    const fetchPurchases = async () => {
      if (!userData?.uid) {
        console.log('No user data available');
        setPurchases([]);
        setLoading(false);
        return;
      }

      // Check if user is a client
      if (userData.role !== Role.Client) {
        console.log('User is not a client, role:', userData.role);
        setPurchases([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        console.log('Fetching purchases for client:', userData.uid);
        const purchasesData = await getPurchasesByClient(userData.uid);
        console.log('Fetched purchases:', purchasesData);
        setPurchases(purchasesData || []);
      } catch (error) {
        console.error('Error fetching purchases:', error);
        setPurchases([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPurchases();
  }, [userData?.uid, userData?.role]);

  const handleViewDetails = (id: string) => {
    navigate(`/trq/purchases/${id}`);
  };

  const handleRowClick = (purchaseId: string) => {
    navigate(`/trq/purchases/${purchaseId}`);
  };

  const filteredPurchases = purchases.filter((purchase) => {
    const matchesSearch = purchase.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || purchase.paymentStatus === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Calculate summary statistics
  const totalAmount = filteredPurchases.reduce((sum, purchase) => sum + purchase.totalPrice, 0);
  const completedPurchases = filteredPurchases.filter((p) => p.paymentStatus === PaymentStatus.Completed).length;
  const pendingPurchases = filteredPurchases.filter((p) => p.paymentStatus === PaymentStatus.Pending).length;
  
  // Calculate questionnaire breakdown
  const questionnaireBreakdown = filteredPurchases.reduce((acc, purchase) => {
    purchase.questionnaires?.forEach(q => {
      const key = q.name;
      if (acc[key]) {
        acc[key] += q.quantity;
      } else {
        acc[key] = q.quantity;
      }
    });
    return acc;
  }, {} as Record<string, number>);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      [PaymentStatus.Pending]: {
        icon: <PendingIcon fontSize="small" />,
        label: 'Pending',
        color: 'warning'
      },
      [PaymentStatus.Completed]: {
        icon: <CheckCircleIcon fontSize="small" />,
        label: 'Completed',
        color: 'success'
      },
      [PaymentStatus.Failed]: {
        icon: <CreditCardIcon fontSize="small" />,
        label: 'Failed',
        color: 'error'
      },
      [PaymentStatus.Refunded]: {
        icon: <ReceiptIcon fontSize="small" />,
        label: 'Refunded',
        color: 'info'
      }
    };

    const config = statusConfig[status as PaymentStatus] || statusConfig[PaymentStatus.Pending];

    return (
      <Chip
        icon={config.icon}
        label={config.label}
        color={config.color as any}
        size="small"
        variant="outlined"
        sx={{
          '& .MuiChip-icon': { fontSize: '1rem' }
        }}
      />
    );
  };

  const columns: GridColDef[] = [
    {
      field: 'purchaseDate',
      headerName: intl.formatMessage({ id: 'purchase-date' }) || 'Purchase Date',
      flex: 0.8,
      width: 200,
      renderCell: (params: GridRenderCellParams<Purchase>) => {
        const purchaseDate = params.row.purchaseDate;
        const date = purchaseDate?.toDate ? purchaseDate.toDate() : new Date(purchaseDate);
        return (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CalendarTodayIcon sx={{ color: theme.palette.primary.main, mr: 1, fontSize: '1rem' }} />
            <Typography variant="body2">
              {date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })}
            </Typography>
          </Box>
        );
      }
    },
    {
      field: 'questionnaires',
      headerName: intl.formatMessage({ id: 'questionnaires' }) || 'Questionnaires',
      flex: 1.5,
      renderCell: (params: GridRenderCellParams<Purchase>) => {
        const questionnaires = params.row.questionnaires || [];
        const purchaseId = params.row.id;
        const isExpanded = expandedRows.has(purchaseId);
        
        const toggleExpanded = (event: React.MouseEvent) => {
          event.stopPropagation();
          const newExpanded = new Set(expandedRows);
          if (isExpanded) {
            newExpanded.delete(purchaseId);
          } else {
            newExpanded.add(purchaseId);
          }
          setExpandedRows(newExpanded);
        };

        if (questionnaires.length === 0) {
          return (
            <Typography variant="body2" color="text.secondary">
              No questionnaires
            </Typography>
          );
        }

        const displayedQuestionnaires = isExpanded ? questionnaires : questionnaires.slice(0, 1);
        const hasMore = questionnaires.length > 1;

        return (
          <Box sx={{ py: 1, width: '100%' }}>
            {displayedQuestionnaires.map((q, index) => (
              <Box key={q.id || index} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <AssignmentIcon sx={{ color: theme.palette.primary.main, mr: 1, fontSize: '0.9rem' }} />
                <Typography variant="body2" sx={{ mr: 1, flexGrow: 1 }} noWrap>
                  {q.name}
                </Typography>
                <Chip
                  label={`×${q.quantity}`}
                  size="small"
                  variant="outlined"
                  color="primary"
                  sx={{ 
                    height: '20px',
                    fontSize: '0.7rem',
                    '& .MuiChip-label': { px: 1 }
                  }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                  ${q.unitPrice?.toFixed(2)}
                </Typography>
              </Box>
            ))}
            
            {hasMore && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                <IconButton 
                  size="small" 
                  onClick={toggleExpanded}
                  sx={{ p: 0.5 }}
                >
                  {isExpanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
                </IconButton>
                <Typography variant="caption" color="text.secondary" sx={{ ml: 0.5 }}>
                  {isExpanded 
                    ? 'Show less' 
                    : `+${questionnaires.length - 1} more`
                  }
                </Typography>
              </Box>
            )}
          </Box>
        );
      }
    },
    {
      field: 'totalQuantity',
      headerName: intl.formatMessage({ id: 'total-quantity' }) || 'Total Qty',
      flex: 0.5,
      align: 'center',
      renderCell: (params: GridRenderCellParams<Purchase>) => (
        <Typography variant="body2" fontWeight="medium">
          {params.row.totalQuantity}
        </Typography>
      )
    },
    {
      field: 'totalPrice',
      headerName: intl.formatMessage({ id: 'total-price' }) || 'Amount',
      flex: 0.8,
      align: 'right',
      renderCell: (params: GridRenderCellParams<Purchase>) => (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
          <AttachMoneyIcon sx={{ color: theme.palette.success.main, mr: 0.5, fontSize: '1.1rem' }} />
          <Typography variant="body2" fontWeight="medium">
            {new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD'
            }).format(params.row.totalPrice)}
          </Typography>
        </Box>
      )
    },
    {
      field: 'paymentStatus',
      headerName: intl.formatMessage({ id: 'payment-status' }) || 'Status',
      flex: 0.8,
      align: 'center',
      headerAlign: 'center',
      renderCell: (params: GridRenderCellParams<Purchase>) => {
        return (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%' }}>
            {getStatusChip(params.row.paymentStatus)}
          </Box>
        );
      }
    },
    {
      field: 'actions',
      headerName: intl.formatMessage({ id: 'actions' }) || 'Actions',
      flex: 0.6,
      sortable: false,
      align: 'center',
      renderCell: (params: GridRenderCellParams<Purchase>) => (
        <Stack direction="row" spacing={1}>
          <Tooltip title="View Details">
            <IconButton
              color="primary"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleViewDetails(params.row.id);
              }}
            >
              <VisibilityTwoToneIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Stack>
      )
    }
  ];

  // Custom toolbar
  const CustomToolbar = () => {
    return (
      <GridToolbarContainer>
        <Box display="flex" justifyContent="space-between" width="100%" alignItems="center" p={1.5}>
          <Typography variant="h4" sx={{ fontWeight: 600 }}>
            {intl.formatMessage({ id: 'my-purchases' }) || 'Purchase History'}
          </Typography>
          <Box sx={{ marginLeft: 'auto' }}>
            <Button
              color="primary"
              variant="contained"
              startIcon={<ShoppingCartIcon />}
              onClick={() => navigate('/trq/purchases/cart')}
              size="small"
              sx={{ borderRadius: 2 }}
            >
              {intl.formatMessage({ id: 'new-purchase' }) || 'New Purchase'}
            </Button>
          </Box>
        </Box>
      </GridToolbarContainer>
    );
  };

  return (
    <MainCard>
      <Grid container spacing={2}>
        {/* Page title with info */}
        <Grid item xs={12}>
          <Box mb={3}>
            <Typography variant="h3" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
              {intl.formatMessage({ id: 'my-purchases' }) || 'My Purchases'}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
              {userData?.role === Role.Client
                ? intl.formatMessage({ id: 'view-purchase-history' }) || 'Track and review all your purchases in one place'
                : 'Purchase history is only available to client users'}
            </Typography>
          </Box>
        </Grid>

        {/* Summary cards */}
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={3}
            sx={{
              p: 2.5,
              display: 'flex',
              flexDirection: 'column',
              borderRadius: 2,
              height: '100%',
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                width: '100%',
                height: '5px',
                top: 0,
                left: 0,
                backgroundColor: theme.palette.primary.main
              }
            }}
          >
            <Box sx={{ position: 'relative', zIndex: 1 }}>
              <Avatar
                sx={{
                  width: 48,
                  height: 48,
                  bgcolor: 'primary.light',
                  color: 'primary.dark',
                  mb: 2
                }}
              >
                <ShoppingCartIcon />
              </Avatar>
              <Typography variant="h4" fontWeight="medium" gutterBottom>
                {purchases.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {intl.formatMessage({ id: 'total-purchases' }) || 'Total Purchases'}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={3}
            sx={{
              p: 2.5,
              display: 'flex',
              flexDirection: 'column',
              borderRadius: 2,
              height: '100%',
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                width: '100%',
                height: '5px',
                top: 0,
                left: 0,
                backgroundColor: theme.palette.success.main
              }
            }}
          >
            <Box sx={{ position: 'relative', zIndex: 1 }}>
              <Avatar
                sx={{
                  width: 48,
                  height: 48,
                  bgcolor: 'success.light',
                  color: 'success.dark',
                  mb: 2
                }}
              >
                <AttachMoneyIcon />
              </Avatar>
              <Typography variant="h4" fontWeight="medium" gutterBottom>
                {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'USD'
                }).format(totalAmount)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {intl.formatMessage({ id: 'total-amount' }) || 'Total Amount'}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={3}
            sx={{
              p: 2.5,
              display: 'flex',
              flexDirection: 'column',
              borderRadius: 2,
              height: '100%',
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                width: '100%',
                height: '5px',
                top: 0,
                left: 0,
                backgroundColor: theme.palette.success.main
              }
            }}
          >
            <Box sx={{ position: 'relative', zIndex: 1 }}>
              <Avatar
                sx={{
                  width: 48,
                  height: 48,
                  bgcolor: 'success.light',
                  color: 'success.dark',
                  mb: 2
                }}
              >
                <CheckCircleIcon />
              </Avatar>
              <Typography variant="h4" fontWeight="medium" gutterBottom>
                {completedPurchases}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {intl.formatMessage({ id: 'completed-purchases' }) || 'Completed Purchases'}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={3}
            sx={{
              p: 2.5,
              display: 'flex',
              flexDirection: 'column',
              borderRadius: 2,
              height: '100%',
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                width: '100%',
                height: '5px',
                top: 0,
                left: 0,
                backgroundColor: theme.palette.warning.main
              }
            }}
          >
            <Box sx={{ position: 'relative', zIndex: 1 }}>
              <Avatar
                sx={{
                  width: 48,
                  height: 48,
                  bgcolor: 'warning.light',
                  color: 'warning.dark',
                  mb: 2
                }}
              >
                <PendingIcon />
              </Avatar>
              <Typography variant="h4" fontWeight="medium" gutterBottom>
                {pendingPurchases}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {intl.formatMessage({ id: 'pending-purchases' }) || 'Pending Purchases'}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Questionnaire Breakdown Summary */}
        <Grid item xs={12}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              borderRadius: 2,
              mb: 2
            }}
          >
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
              {intl.formatMessage({ id: 'questionnaire-breakdown' }) || 'Questionnaire Breakdown'}
            </Typography>
            {Object.keys(questionnaireBreakdown).length > 0 ? (
              <Grid container spacing={2}>
                {Object.entries(questionnaireBreakdown).map(([name, quantity]) => (
                  <Grid item xs={12} sm={6} md={4} key={name}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        p: 2,
                        bgcolor: 'background.default',
                        borderRadius: 1,
                        border: `1px solid ${theme.palette.divider}`
                      }}
                    >
                      <AssignmentIcon sx={{ color: theme.palette.primary.main, mr: 2 }} />
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body1" fontWeight="medium" noWrap>
                          {name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {quantity} purchased
                        </Typography>
                      </Box>
                      <Chip
                        label={quantity}
                        color="primary"
                        size="small"
                        sx={{ fontWeight: 600 }}
                      />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No questionnaires purchased yet.
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* Search and filter section */}
        <Grid item xs={12}>
          <Paper
            elevation={3}
            sx={{
              p: 2,
              borderRadius: 2,
              mb: 2
            }}
          >
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={2}
              alignItems={{ xs: 'flex-start', sm: 'center' }}
              justifyContent="space-between"
            >
              <Typography variant="h5" sx={{ mb: { xs: 1, sm: 0 } }}>
                {intl.formatMessage({ id: 'filter-purchases' }) || 'Filter Purchases'}
              </Typography>
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} width={{ xs: '100%', sm: 'auto' }}>
                <TextField
                  size="small"
                  placeholder={intl.formatMessage({ id: 'search-purchases' }) || 'Search purchases...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />,
                    sx: { borderRadius: 2 }
                  }}
                  sx={{ minWidth: { xs: '100%', sm: 220 } }}
                />
                <TextField
                  select
                  size="small"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                  InputProps={{
                    sx: { borderRadius: 2 }
                  }}
                  sx={{ minWidth: { xs: '100%', sm: 150 } }}
                >
                  <MenuItem value="all">{intl.formatMessage({ id: 'all-statuses' }) || 'All Statuses'}</MenuItem>
                  {Object.values(PaymentStatus).map((status) => (
                    <MenuItem key={status} value={status}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        {getStatusChip(status)}
                      </Stack>
                    </MenuItem>
                  ))}
                </TextField>
              </Stack>
            </Stack>
          </Paper>
        </Grid>

        {/* Purchase list */}
        <Grid item xs={12}>
          <Paper
            elevation={3}
            sx={{
              borderRadius: 2,
              overflow: 'hidden',
              height: 'calc(100vh - 460px)',
              minHeight: 400
            }}
          >
            <DataGrid
              rows={filteredPurchases}
              columns={columns}
              loading={loading}
              onRowClick={(params) => handleRowClick(params.row.id)}
              sx={{
                ...dataGridStyles,
                '& .MuiDataGrid-row': {
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: theme.palette.action.hover
                  }
                },
                '& .MuiDataGrid-cell': {
                  padding: '16px',
                  '&:focus': {
                    outline: 'none'
                  }
                },
                border: 'none',
                borderRadius: 2
              }}
              slots={{
                toolbar: CustomToolbar
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              initialState={{
                pagination: {
                  paginationModel: { pageSize: 10, page: 0 }
                }
              }}
            />
          </Paper>
        </Grid>

        {/* Empty state if no purchases */}
        {filteredPurchases.length === 0 && !loading && (
          <Grid item xs={12}>
            <Paper
              elevation={3}
              sx={{
                p: 4,
                textAlign: 'center',
                borderRadius: 2,
                bgcolor: 'background.paper'
              }}
            >
              <ShoppingCartIcon sx={{ fontSize: 60, color: 'action.disabled', mb: 2 }} />
              <Typography variant="h5" gutterBottom>
                {userData?.role !== Role.Client
                  ? 'Access Restricted'
                  : intl.formatMessage({ id: 'no-purchases-found' }) || 'No Purchases Found'}
              </Typography>
              <Typography variant="body1" color="textSecondary" paragraph>
                {userData?.role !== Role.Client
                  ? 'This page is only available to client users. Please contact your administrator if you believe this is an error.'
                  : searchTerm || statusFilter !== 'all'
                  ? intl.formatMessage({ id: 'no-matching-purchases' }) || 'No purchases match your search criteria.'
                  : intl.formatMessage({ id: 'no-purchases-yet' }) || "You haven't made any purchases yet."}
              </Typography>
              {userData?.role === Role.Client && (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<ShoppingCartIcon />}
                  onClick={() => navigate('/trq/purchases/cart')}
                  sx={{ mt: 2, borderRadius: 2 }}
                >
                  {intl.formatMessage({ id: 'browse-products' }) || 'Browse Products'}
                </Button>
              )}
            </Paper>
          </Grid>
        )}
      </Grid>
    </MainCard>
  );
};

export default MyPurchases;

import React from 'react';
import { useIntl } from 'react-intl';
import { Grid, Box, Typography, Paper } from '@mui/material';

// project imports
import MainCard from '[components]/cards/MainCard';
import { Role } from 'RBAC/[types]/Role';

// custom hooks
import { usePurchases } from '../[hooks]/usePurchases';
import { usePurchaseFilters } from '../[hooks]/usePurchaseFilters';

// components
import PurchaseSummaryCards from '../[components]/PurchaseSummaryCards';
import QuestionnaireBreakdown from '../[components]/QuestionnaireBreakdown';
import PurchaseFilters from '../[components]/PurchaseFilters';
import PurchaseDataGrid from '../[components]/PurchaseDataGrid';
import PurchaseEmptyState from '../[components]/PurchaseEmptyState';
import PurchaseLoadingState from '../[components]/PurchaseLoadingState';

// utils
import { getStatusChip } from '../[utils]/purchaseUtils';

const MyPurchases: React.FC = () => {
  const intl = useIntl();

  // Custom hooks for state management
  const {
    searchTerm,
    statusFilter,
    expandedRows,
    handleSearchChange,
    handleStatusFilterChange,
    toggleRowExpansion,
    clearFilters
  } = usePurchaseFilters();

  const {
    purchases,
    filteredPurchases,
    loading,
    error,
    isClientUser,
    stats,
    refetch
  } = usePurchases({ searchTerm, statusFilter });

  // Derived state
  const hasFilters = searchTerm !== '' || statusFilter !== 'all';
  const showEmptyState = !loading && filteredPurchases.length === 0;



  return (
    <MainCard>
      <Grid container spacing={2}>
        {/* Page title with info */}
        <Grid item xs={12}>
          <Box mb={3}>
            <Typography variant="h3" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
              {intl.formatMessage({ id: 'my-purchases' }) || 'My Purchases'}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
              {isClientUser
                ? intl.formatMessage({ id: 'view-purchase-history' }) || 'Track and review all your purchases in one place'
                : 'Purchase history is only available to client users'}
            </Typography>
            {error && (
              <Paper
                sx={{
                  p: 2,
                  mt: 2,
                  bgcolor: 'error.light',
                  color: 'error.contrastText',
                  borderRadius: 1
                }}
              >
                <Typography variant="body2">
                  Error loading purchases: {error}
                </Typography>
              </Paper>
            )}
          </Box>
        </Grid>

        {/* Loading State */}
        {loading && <PurchaseLoadingState />}

        {/* Content when not loading */}
        {!loading && (
          <>
            {/* Summary cards */}
            <PurchaseSummaryCards stats={stats} />

            {/* Questionnaire Breakdown */}
            <QuestionnaireBreakdown questionnaireBreakdown={stats.questionnaireBreakdown} />

            {/* Search and filter section */}
            <PurchaseFilters
              searchTerm={searchTerm}
              statusFilter={statusFilter}
              onSearchChange={handleSearchChange}
              onStatusFilterChange={handleStatusFilterChange}
              onClearFilters={clearFilters}
              getStatusChip={getStatusChip}
            />

            {/* Purchase list or empty state */}
            {showEmptyState ? (
              <PurchaseEmptyState
                isClientUser={isClientUser}
                hasFilters={hasFilters}
                searchTerm={searchTerm}
                statusFilter={statusFilter}
              />
            ) : (
              <Grid item xs={12}>
                <Paper
                  elevation={3}
                  sx={{
                    borderRadius: 2,
                    overflow: 'hidden',
                    height: 'calc(100vh - 460px)',
                    minHeight: 400
                  }}
                >
                  <PurchaseDataGrid
                    purchases={filteredPurchases}
                    loading={loading}
                    expandedRows={expandedRows}
                    onToggleRowExpansion={toggleRowExpansion}
                  />
                </Paper>
              </Grid>
            )}
          </>
        )}
      </Grid>
    </MainCard>
  );
};

export default MyPurchases;

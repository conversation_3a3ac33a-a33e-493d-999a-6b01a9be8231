import React from 'react';
import { GridToolbarContainer } from '@mui/x-data-grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import AddIcon from '@mui/icons-material/Add';
import AssignmentIndIcon from '@mui/icons-material/AssignmentInd';
import SearchIcon from '@mui/icons-material/Search';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import RateReviewIcon from '@mui/icons-material/RateReview';

interface QuestionnaireToolbarProps {
  intl: any;
  selectedRows: string[];
  permissions: any;
  questionnaires: any[];
  statusFilter: string;
  searchTerm: string;
  handleView: (id: string) => void;
  handleReview: (id: string) => void;
  handleDelete: (id: string) => void;
  handleOpenAssignDialog: () => void;
  handleSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleStatusFilterChange: (event: any) => void;
  setTemplateDialogOpen: (open: boolean) => void;
}

const QuestionnaireToolbar: React.FC<QuestionnaireToolbarProps> = ({
  intl,
  selectedRows,
  permissions,
  questionnaires,
  statusFilter,
  searchTerm,
  handleView,
  handleReview,
  handleDelete,
  handleOpenAssignDialog,
  handleSearchChange,
  handleStatusFilterChange,
  setTemplateDialogOpen
}) => {
  const QuestionnaireStatus = {
    Created: 'created',
    Assigned: 'assigned',
    InProgress: 'in-progress',
    Completed: 'completed',
    Reviewed: 'reviewed'
  };
  const selectedQuestionnaires = questionnaires.filter((q) => selectedRows.includes(q.id));
  const allInCreatedState =
    selectedQuestionnaires.length > 0 && selectedQuestionnaires.every((q) => q.status === QuestionnaireStatus.Created);
  const canAssign = selectedRows.length > 0 && allInCreatedState && permissions.canUpdateQuestionnaire();
  const canPerformSingleActions = selectedRows.length === 1;
  const canDelete = selectedRows.length > 0 && permissions.canDeleteQuestionnaire();
  const canView = canPerformSingleActions && permissions.canViewQuestionnaire();
  const canReview = canPerformSingleActions && permissions.canUpdateQuestionnaire();
  const canCreate = permissions.canCreateQuestionnaire();

  return (
    <GridToolbarContainer sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider', flexDirection: 'column' }}>
      <Stack direction="row" spacing={2} alignItems="center" width="100%" justifyContent="space-between" sx={{ mb: 2 }}>
        <Typography variant="h4">{intl.formatMessage({ id: 'all-questionnaires' }) || 'All Questionnaires'}</Typography>
        <Stack direction="row" spacing={1} flexWrap="wrap">
          {canView && (
            <Button variant="outlined" color="info" size="small" startIcon={<VisibilityIcon />} onClick={() => handleView(selectedRows[0])}>
              {intl.formatMessage({ id: 'view' }) || 'View'}
            </Button>
          )}
          {canReview && (
            <Button
              variant="outlined"
              color="secondary"
              size="small"
              startIcon={<RateReviewIcon />}
              onClick={() => handleReview(selectedRows[0])}
            >
              {intl.formatMessage({ id: 'review' }) || 'Review'}
            </Button>
          )}
          {canDelete && (
            <Button variant="outlined" color="error" size="small" startIcon={<DeleteIcon />} onClick={() => handleDelete(selectedRows[0])}>
              {intl.formatMessage({ id: 'delete' }) || 'Delete'} ({selectedRows.length})
            </Button>
          )}
          {canAssign && (
            <Button
              variant="outlined"
              color="secondary"
              size="small"
              startIcon={<AssignmentIndIcon />}
              onClick={handleOpenAssignDialog}
              data-testid="questionnaire-assign-button"
            >
              {intl.formatMessage({ id: 'assign' }) || 'Assign'} ({selectedRows.length})
            </Button>
          )}
          {selectedRows.length === 0 && canCreate && (
            <Button
              variant="outlined"
              color="primary"
              size="small"
              startIcon={<AddIcon />}
              onClick={() => setTemplateDialogOpen(true)}
              data-testid="create-questionnaire-from-template-button"
            >
              {intl.formatMessage({ id: 'create-from-template' }) || 'Create from Template'}
            </Button>
          )}
        </Stack>
      </Stack>
      <Stack direction="row" spacing={1} alignItems="center" sx={{ justifyContent: 'flex-end', width: '100%' }}>
        <TextField
          size="small"
          variant="outlined"
          placeholder={intl.formatMessage({ id: 'search' }) || 'Search...'}
          value={searchTerm}
          onChange={handleSearchChange}
          data-testid="questionnaire-search-input"
          InputProps={{
            startAdornment: <SearchIcon fontSize="small" sx={{ mr: 0.5 }} />
          }}
        />
        <FormControl size="small" sx={{ minWidth: 130 }}>
          <InputLabel>{intl.formatMessage({ id: 'status' }) || 'Status'}</InputLabel>
          <Select
            value={statusFilter}
            onChange={handleStatusFilterChange}
            label={intl.formatMessage({ id: 'status' }) || 'Status'}
            data-testid="questionnaire-status-filter"
          >
            <MenuItem value="all">{intl.formatMessage({ id: 'all' }) || 'All'}</MenuItem>
            <MenuItem value="created">{intl.formatMessage({ id: 'created' }) || 'Created'}</MenuItem>
            <MenuItem value="assigned">{intl.formatMessage({ id: 'assigned' }) || 'Assigned'}</MenuItem>
            <MenuItem value="in-progress">{intl.formatMessage({ id: 'in-progress' }) || 'In Progress'}</MenuItem>
            <MenuItem value="completed">{intl.formatMessage({ id: 'completed' }) || 'Completed'}</MenuItem>
            <MenuItem value="reviewed">{intl.formatMessage({ id: 'reviewed' }) || 'Reviewed'}</MenuItem>
          </Select>
        </FormControl>
      </Stack>
    </GridToolbarContainer>
  );
};

export default QuestionnaireToolbar;

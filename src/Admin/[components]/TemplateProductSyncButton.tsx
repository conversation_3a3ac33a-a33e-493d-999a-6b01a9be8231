import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>ert, Box, Typography, CircularProgress } from '@mui/material';
import { Sync as SyncIcon } from '@mui/icons-material';
import { syncAllPublishedTemplates } from '../../Products/[services]/templateProductSyncService';

interface SyncResult {
  created: number;
  errors: string[];
}

const TemplateProductSyncButton: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [result, setResult] = useState<SyncResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSync = async () => {
    setIsRunning(true);
    setResult(null);
    setError(null);

    try {
      console.log('Starting template-to-product sync...');
      const syncResult = await syncAllPublishedTemplates();
      console.log('Sync completed:', syncResult);
      setResult(syncResult);
    } catch (err: any) {
      console.error('Sync failed:', err);
      setError(err?.message || 'Failed to sync templates to products');
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        Template-Product Sync
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Sync all published questionnaire templates to the products marketplace. 
        This will create product entries for any published templates that don't already have them.
      </Typography>
      
      <Button
        variant="contained"
        color="primary"
        startIcon={isRunning ? <CircularProgress size={20} color="inherit" /> : <SyncIcon />}
        onClick={handleSync}
        disabled={isRunning}
        sx={{ mb: 2 }}
      >
        {isRunning ? 'Syncing...' : 'Sync Published Templates to Products'}
      </Button>

      {result && (
        <Alert severity="success" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Sync completed successfully!</strong>
          </Typography>
          <Typography variant="body2">
            • {result.created} new products created
          </Typography>
          {result.errors.length > 0 && (
            <Typography variant="body2" color="error">
              • {result.errors.length} errors occurred
            </Typography>
          )}
        </Alert>
      )}

      {result && result.errors.length > 0 && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="body2" gutterBottom>
            <strong>Errors during sync:</strong>
          </Typography>
          {result.errors.map((error, index) => (
            <Typography key={index} variant="body2" component="div">
              • {error}
            </Typography>
          ))}
        </Alert>
      )}

      {error && (
        <Alert severity="error">
          <Typography variant="body2">
            <strong>Sync failed:</strong> {error}
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default TemplateProductSyncButton;

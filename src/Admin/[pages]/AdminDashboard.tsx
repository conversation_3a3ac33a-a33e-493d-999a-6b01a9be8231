import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';

// Firebase
import { getCurrentUserData, getUsers } from '../../Users/<USER>/userService';
import { getUserRoleCounts, getSystemActivityStats } from '../../Analytics/[services]/statsService';
import { TRQUser } from '../../Users/<USER>/User';

// material-ui
import { useTheme } from '@mui/material/styles';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  IconButton,
  TextField,
  InputAdornment,
  Chip,
  Button,
  Tabs,
  Tab,
  Paper,
  LinearProgress
} from '@mui/material';

// charts
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';

// icons
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import DescriptionIcon from '@mui/icons-material/Description';
import SearchIcon from '@mui/icons-material/Search';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AssignmentIcon from '@mui/icons-material/Assignment';
import HomeIcon from '@mui/icons-material/Home';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import BusinessIcon from '@mui/icons-material/Business';
import AssessmentIcon from '@mui/icons-material/Assessment';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import PeopleIcon from '@mui/icons-material/People';
import AddIcon from '@mui/icons-material/Add';
import { Role } from '[types]/Role';

// RBAC permissions
import usePermission from '../../RBAC/[hooks]/usePermission';
import { useRole } from '../../RBAC/[contexts]/RoleContext';

// images
import widget1 from '../../../assets/images/widget/dashboard-1.jpg';
import widget2 from '../../../assets/images/widget/dashboard-2.jpg';
import ROUTES from 'Routing/appRoutes';
// Updated imports for admin dashboard helpers
import { getUserRegistrationsByMonth } from '../[components]/dashboardChartService';
import { useDashboardRealtime } from '../[components]/useDashboardRealtime';
import TemplateProductSyncButton from '../[components]/TemplateProductSyncButton';

// Dashboard component
const AdminDashboard = () => {
  const theme = useTheme();
  const { RESOURCES, ACTIONS, checkPermission } = usePermission();
  const { role } = useRole();
  const [searchText, setSearchText] = useState('');
  const [currentUser, setCurrentUser] = useState<TRQUser | null>(null);
  const [patients, setPatients] = useState<TRQUser[]>([]);
  const [roleCounts, setRoleCounts] = useState({
    doctorCount: 0,
    patientCount: 0,
    clinicCount: 0
  });
  const [activityStats, setActivityStats] = useState({
    questionnaireCount: 0,
    reportsCount: 0,
    lastUpdated: ''
  });
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);

  // New state for chart data
  const [userChartSeries, setUserChartSeries] = useState([
    { name: 'Patients', data: Array(12).fill(0) },
    { name: 'Doctors', data: Array(12).fill(0) }
  ]);
  const [userChartCategories, setUserChartCategories] = useState([
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec'
  ]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Permission checks - memoized to avoid recreating on every render
  const canReadClinicSettings = useCallback(() => {
    return checkPermission(RESOURCES.CLINIC_SETTINGS, ACTIONS.READ);
  }, [checkPermission, RESOURCES.CLINIC_SETTINGS, ACTIONS.READ]);

  const canManageUsers = useCallback(() => {
    return checkPermission(RESOURCES.USERS, ACTIONS.MANAGE);
  }, [checkPermission, RESOURCES.USERS, ACTIONS.MANAGE]);

  // Fetch chart data
  const fetchChartData = useCallback(async () => {
    const [patients, doctors] = await Promise.all([getUserRegistrationsByMonth(Role.Patient), getUserRegistrationsByMonth(Role.Doctor)]);
    setUserChartCategories(patients.map((m) => m.month));
    setUserChartSeries([
      { name: 'Patients', data: patients.map((m) => m.count) },
      { name: 'Doctors', data: doctors.map((m) => m.count) }
    ]);
  }, []);

  // Real-time listeners
  useDashboardRealtime({
    onUsersChange: fetchChartData,
    onQuestionnairesChange: fetchChartData
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch current user data
        const userData = await getCurrentUserData();
        setCurrentUser(userData);

        // For Clinic Admin or full Admin, fetch additional data
        if (canReadClinicSettings()) {
          // Fetch user role counts
          const roleCountsData = await getUserRoleCounts();
          setRoleCounts(roleCountsData);

          // Fetch system activity stats
          const systemStats = await getSystemActivityStats();
          setActivityStats(systemStats);

          // Fetch recent patients
          const recentPatients = await getUsers({
            role: Role.Patient,
            isActive: true,
            limit: 5,
            orderByField: 'updatedAt',
            orderDirection: 'desc'
          });
          setPatients(recentPatients);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
    fetchChartData();
    // eslint-disable-next-line
  }, [role, canReadClinicSettings]);

  // Filter patients based on search term
  const filteredPatients = patients.filter((patient) => patient.email && patient.email.toLowerCase().includes(searchText.toLowerCase()));

  // Handlers for search
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(event.target.value);
  };

  // Redirect non-admin users
  if (currentUser?.role === Role.Patient || currentUser?.role === Role.Client) {
    return (
      <Box sx={{ p: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '50vh' }}>
        <Typography variant="h4" color="error" gutterBottom>
          Access Denied
        </Typography>
        <Typography variant="body1" paragraph>
          You don't have permission to access the Admin Dashboard.
        </Typography>
        <Button component={Link} to="/" variant="contained" color="primary" startIcon={<HomeIcon />}>
          Go to Home
        </Button>
      </Box>
    );
  }

  // Loading state
  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Admin Dashboard
        </Typography>
        <LinearProgress />
        <Typography variant="body2" sx={{ mt: 2 }}>
          Loading dashboard data...
        </Typography>
      </Box>
    );
  }

  // Render the Clinic Admin View
  const renderClinicAdminView = () => {
    return (
      <Grid container spacing={3}>
        {/* Header with Tabs */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h4" component="h1">
                Clinic Administration
              </Typography>
              <Chip icon={<BusinessIcon />} label="Clinic Admin" color="primary" size="medium" />
            </Box>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="admin dashboard tabs">
              <Tab label="Overview" icon={<HomeIcon />} iconPosition="start" />
              <Tab label="Patients" icon={<PersonAddIcon />} iconPosition="start" />
              <Tab label="Doctors" icon={<LocalHospitalIcon />} iconPosition="start" />
            </Tabs>
          </Paper>
        </Grid>

        {/* Tab content */}
        <Grid item xs={12}>
          {tabValue === 0 && (
            <Grid container spacing={3}>
              {/* Statistics Cards */}
              <Grid item xs={12} md={4}>
                <Card raised>
                  <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ bgcolor: theme.palette.primary.main, mr: 2, width: 56, height: 56 }}>
                      <PersonAddIcon fontSize="large" />
                    </Avatar>
                    <Box>
                      <Typography variant="h3">{roleCounts.patientCount}</Typography>
                      <Typography variant="subtitle1">Total Patients</Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card raised>
                  <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ bgcolor: theme.palette.secondary.main, mr: 2, width: 56, height: 56 }}>
                      <LocalHospitalIcon fontSize="large" />
                    </Avatar>
                    <Box>
                      <Typography variant="h3">{roleCounts.doctorCount}</Typography>
                      <Typography variant="subtitle1">Total Doctors</Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card raised>
                  <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ bgcolor: theme.palette.success.main, mr: 2, width: 56, height: 56 }}>
                      <BusinessIcon fontSize="large" />
                    </Avatar>
                    <Box>
                      <Typography variant="h3">{roleCounts.clinicCount}</Typography>
                      <Typography variant="subtitle1">Total Clinics</Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Chart */}
              <Grid item xs={12} md={8}>
                <Card>
                  <CardHeader
                    title="User Growth"
                    subheader="Monthly patient and doctor registrations"
                    action={
                      <IconButton aria-label="settings">
                        <MoreVertIcon />
                      </IconButton>
                    }
                  />
                  <Divider />
                  <CardContent>
                    <Chart options={userChartOptions} series={userChartSeries} type="area" height={350} />
                  </CardContent>
                </Card>
              </Grid>

              {/* Activity Summary */}
              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%' }}>
                  <CardHeader
                    title="Activity Summary"
                    subheader={`Last updated: ${activityStats.lastUpdated ? new Date(activityStats.lastUpdated).toLocaleString() : 'N/A'}`}
                  />
                  <Divider />
                  <CardContent>
                    <List>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: theme.palette.info.main }}>
                            <AssignmentIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={`${activityStats.questionnaireCount} Questionnaires`}
                          secondary="Total active questionnaires"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: theme.palette.warning.main }}>
                            <AssessmentIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText primary={`${activityStats.reportsCount} Reports`} secondary="Generated patient reports" />
                      </ListItem>
                    </List>
                    {canManageUsers() && (
                      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                        <Button
                          variant="contained"
                          color="primary"
                          component={Link}
                          to={ROUTES.ADMIN.PERMISSIONS}
                          startIcon={<PeopleIcon />}
                        >
                          Manage Permissions
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Featured Banner */}
              <Grid item xs={12}>
                <Card
                  sx={{
                    backgroundImage: `url(${widget1})`,
                    backgroundSize: 'cover',
                    color: 'white',
                    p: 2
                  }}
                >
                  <CardContent sx={{ position: 'relative', zIndex: 1 }}>
                    <Box sx={{ p: 2, backdropFilter: 'blur(4px)', backgroundColor: 'rgba(0,0,0,0.4)', borderRadius: 1 }}>
                      <Typography variant="h4" gutterBottom>
                        Welcome to Your Clinic Dashboard
                      </Typography>
                      <Typography variant="body1" paragraph>
                        Access patient records, manage appointments, and view clinic statistics all in one place.
                      </Typography>
                      <Button variant="contained" color="secondary" component={Link} to={ROUTES.ADMIN.DASHBOARD}>
                        View TRQ Dashboard
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {tabValue === 1 && (
            <Grid container spacing={3}>
              {/* Patients Tab */}
              <Grid item xs={12}>
                <Card>
                  <CardHeader
                    title="Recent Patients"
                    action={
                      <TextField
                        placeholder="Search patients..."
                        value={searchText}
                        onChange={handleSearchChange}
                        variant="outlined"
                        size="small"
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon />
                            </InputAdornment>
                          )
                        }}
                      />
                    }
                  />
                  <Divider />
                  <List sx={{ bgcolor: 'background.paper' }}>
                    {filteredPatients.length > 0 ? (
                      filteredPatients.map((patient) => (
                        <React.Fragment key={patient.uid}>
                          <ListItem
                            secondaryAction={
                              <IconButton component={Link} to={ROUTES.USERS.DETAILS(patient.uid)}>
                                <MoreVertIcon />
                              </IconButton>
                            }
                          >
                            <ListItemAvatar>
                              <Avatar>{patient.email ? patient.email.charAt(0).toUpperCase() : 'U'}</Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={patient.email || 'No name'}
                              secondary={
                                <>
                                  <Typography component="span" variant="body2" color="text.primary">
                                    {patient.email}
                                  </Typography>
                                  <br />
                                  {patient?.lastLogin
                                    ? `Last login: ${new Date(String(patient.lastLogin)).toLocaleDateString()}`
                                    : 'Never logged in'}
                                </>
                              }
                            />
                          </ListItem>
                          <Divider variant="inset" component="li" />
                        </React.Fragment>
                      ))
                    ) : (
                      <ListItem>
                        <ListItemText primary="No patients found" />
                      </ListItem>
                    )}
                  </List>
                  {filteredPatients.length > 0 && (
                    <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
                      <Button variant="outlined" color="primary" component={Link} to="/Patient/Management">
                        View All Patients
                      </Button>
                    </Box>
                  )}
                </Card>
              </Grid>
            </Grid>
          )}

          {tabValue === 2 && (
            <Grid container spacing={3}>
              {/* Doctors Tab */}
              <Grid item xs={12}>
                <Card>
                  <CardHeader
                    title="Doctors Management"
                    action={
                      <Button variant="contained" startIcon={<AddIcon />} component={Link} to={ROUTES.DOCTORS.ADD}>
                        Add Doctor
                      </Button>
                    }
                  />
                  <Divider />
                  <CardContent>
                    <Typography paragraph>Manage doctor profiles, set permissions, and track activity.</Typography>

                    {/* Doctor List would go here */}
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                      <img src={widget2} alt="Doctor Management" style={{ maxWidth: '100%', borderRadius: 8 }} />
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                      <Button variant="contained" color="secondary" component={Link} to={ROUTES.DOCTORS.LIST}>
                        Go to Doctor Management
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </Grid>
      </Grid>
    );
  };

  // Render the Full Admin View
  const renderAdminView = () => {
    return (
      <Grid container spacing={3}>
        {/* Header */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h4" component="h1">
                Admin Dashboard
              </Typography>
              <Box>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<PeopleIcon />}
                  component={Link}
                  to={ROUTES.ADMIN.ROLES}
                  sx={{ mr: 1 }}
                  data-testid="manage-roles-btn"
                >
                  Manage Roles
                </Button>
                <Button variant="outlined" startIcon={<NotificationsActiveIcon />} data-testid="notifications-btn">
                  Notifications
                </Button>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Statistics Cards */}
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card raised>
              <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: theme.palette.primary.main, mr: 2, width: 56, height: 56 }}>
                  <PersonAddIcon fontSize="large" />
                </Avatar>
                <Box>
                  <Typography variant="h3">{roleCounts.patientCount}</Typography>
                  <Typography variant="subtitle1">Patients</Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card raised>
              <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: theme.palette.secondary.main, mr: 2, width: 56, height: 56 }}>
                  <LocalHospitalIcon fontSize="large" />
                </Avatar>
                <Box>
                  <Typography variant="h3">{roleCounts.doctorCount}</Typography>
                  <Typography variant="subtitle1">Doctors</Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card raised>
              <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: theme.palette.warning.main, mr: 2, width: 56, height: 56 }}>
                  <DescriptionIcon fontSize="large" />
                </Avatar>
                <Box>
                  <Typography variant="h3">{activityStats.questionnaireCount}</Typography>
                  <Typography variant="subtitle1">Questionnaires</Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card raised>
              <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: theme.palette.success.main, mr: 2, width: 56, height: 56 }}>
                  <AssessmentIcon fontSize="large" />
                </Avatar>
                <Box>
                  <Typography variant="h3">{activityStats.reportsCount}</Typography>
                  <Typography variant="subtitle1">Reports</Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Main Content */}
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Recent User Activity Chart */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardHeader
                title="System Activity Overview"
                subheader="Patient and doctor activity trends"
                action={
                  <IconButton>
                    <MoreVertIcon />
                  </IconButton>
                }
              />
              <Divider />
              <CardContent>
                <Chart options={userChartOptions} series={userChartSeries} type="area" height={350} />
              </CardContent>
            </Card>
          </Grid>

          {/* Quick Actions */}
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%' }}>
              <CardHeader title="Admin Actions" />
              <Divider />
              <CardContent>
                <List>
                  <ListItem component={Link} to={ROUTES.ADMIN.PERMISSIONS}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: theme.palette.primary.main }}>
                        <PeopleIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText primary="Manage Permissions" secondary="Assign roles and permissions to users" />
                  </ListItem>
                  <ListItem component={Link} to={ROUTES.ADMIN.ROLES} data-testid="role-management-link">
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: theme.palette.secondary.main }}>
                        <BusinessIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText primary="Role Management" secondary="Configure system roles and access levels" />
                  </ListItem>
                  <ListItem component={Link} to={ROUTES.ADMIN.DASHBOARD} data-testid="trq-dashboard-link">
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: theme.palette.info.main }}>
                        <AssignmentIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText primary="TRQ Dashboard" secondary="Specialized TRQ system overview" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Template-Product Sync */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="System Maintenance" />
              <Divider />
              <CardContent>
                <TemplateProductSyncButton />
              </CardContent>
            </Card>
          </Grid>

          {/* Featured Content */}
          <Grid item xs={12}>
            <Card
              sx={{
                backgroundImage: `url(${widget1})`,
                backgroundSize: 'cover',
                color: 'white',
                p: 2
              }}
            >
              <CardContent sx={{ position: 'relative', zIndex: 1 }}>
                <Box sx={{ p: 2, backdropFilter: 'blur(4px)', backgroundColor: 'rgba(0,0,0,0.4)', borderRadius: 1 }}>
                  <Typography variant="h4" gutterBottom>
                    Admin Control Center
                  </Typography>
                  <Typography variant="body1" paragraph>
                    Access all administrative functions, manage users, and monitor system performance.
                  </Typography>
                  <Button
                    variant="contained"
                    color="secondary"
                    component={Link}
                    to={ROUTES.ADMIN.DASHBOARD}
                    data-testid="view-complete-system-overview-btn"
                  >
                    View Complete System Overview
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Patients */}
          <Grid item xs={12}>
            <Card>
              <CardHeader
                title="Recent Patient Activity"
                action={
                  <Button
                    size="small"
                    variant="outlined"
                    component={Link}
                    to={ROUTES.USERS.LIST}
                    endIcon={<MoreVertIcon />}
                    data-testid="view-all-patients-btn"
                  >
                    View All
                  </Button>
                }
              />
              <Divider />
              <List sx={{ bgcolor: 'background.paper' }}>
                {filteredPatients.length > 0 ? (
                  filteredPatients.slice(0, 3).map((patient) => (
                    <React.Fragment key={patient.uid}>
                      <ListItem
                        secondaryAction={
                          <IconButton component={Link} to={ROUTES.USERS.DETAILS(patient.uid)}>
                            <MoreVertIcon />
                          </IconButton>
                        }
                      >
                        <ListItemAvatar>
                          <Avatar>{patient.email ? patient.email.charAt(0).toUpperCase() : 'U'}</Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={patient.email || 'No name'}
                          secondary={
                            <>
                              <Typography component="span" variant="body2" color="text.primary">
                                {patient.email}
                              </Typography>
                              <br />
                              {patient?.lastLogin
                                ? `Last login: ${new Date(String(patient.lastLogin)).toLocaleDateString()}`
                                : 'Never logged in'}
                            </>
                          }
                        />
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))
                ) : (
                  <ListItem>
                    <ListItemText primary="No patient data available" secondary="Patient data will appear here once available" />
                  </ListItem>
                )}
              </List>
            </Card>
          </Grid>
        </Grid>
      </Grid>
    );
  };

  // Chart options for user growth
  const userChartOptions: ApexOptions = {
    chart: {
      type: 'area',
      height: 350,
      toolbar: {
        show: false
      }
    },
    dataLabels: {
      enabled: false
    },
    stroke: {
      curve: 'smooth',
      width: 2
    },
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.9,
        stops: [0, 90, 100]
      }
    },
    colors: [theme.palette.primary.main, theme.palette.secondary.main],
    xaxis: {
      categories: userChartCategories
    },
    yaxis: {
      title: {
        text: 'User Count'
      }
    },
    tooltip: {
      x: {
        format: 'dd/MM/yy HH:mm'
      }
    }
  };

  return (
    <Box data-testid="dashboard-admin" sx={{ p: 3 }}>
      {/* Show different views based on role */}
      {currentUser?.role === Role.ClinicAdmin ? renderClinicAdminView() : renderAdminView()}
    </Box>
  );
};

export default AdminDashboard;

/**
 * Authentication State Manager for E2E Tests
 * 
 * This utility manages authentication states to avoid repeated logins
 * during test execution, significantly speeding up test runs.
 */

import { Page, BrowserContext } from '@playwright/test';
import { Role } from '../../src/[types]/Role';
import { loginAs } from './auth-utils';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Directory to store authentication states
const AUTH_STATES_DIR = path.join(__dirname, '..', '.auth-states');

// Ensure auth states directory exists
if (!fs.existsSync(AUTH_STATES_DIR)) {
  fs.mkdirSync(AUTH_STATES_DIR, { recursive: true });
}

// Cache for authentication states in memory
const authStateCache = new Map<Role, any>();

/**
 * Get the file path for storing authentication state for a role
 */
function getAuthStatePath(role: Role): string {
  return path.join(AUTH_STATES_DIR, `${role.toLowerCase()}-auth.json`);
}

/**
 * Check if authentication state exists and is recent (less than 1 hour old)
 */
function isAuthStateValid(role: Role): boolean {
  const authStatePath = getAuthStatePath(role);
  
  if (!fs.existsSync(authStatePath)) {
    return false;
  }
  
  const stats = fs.statSync(authStatePath);
  const ageInMs = Date.now() - stats.mtime.getTime();
  const maxAgeMs = 60 * 60 * 1000; // 1 hour
  
  return ageInMs < maxAgeMs;
}

/**
 * Save authentication state to file
 */
async function saveAuthState(context: BrowserContext, role: Role): Promise<void> {
  const authStatePath = getAuthStatePath(role);
  const state = await context.storageState();
  
  fs.writeFileSync(authStatePath, JSON.stringify(state, null, 2));
  authStateCache.set(role, state);
  
  console.log(`💾 Saved authentication state for ${role}`);
}

/**
 * Load authentication state from file
 */
function loadAuthState(role: Role): any | null {
  // Check memory cache first
  if (authStateCache.has(role)) {
    console.log(`📋 Using cached authentication state for ${role}`);
    return authStateCache.get(role);
  }
  
  const authStatePath = getAuthStatePath(role);
  
  if (!fs.existsSync(authStatePath)) {
    return null;
  }
  
  try {
    const state = JSON.parse(fs.readFileSync(authStatePath, 'utf-8'));
    authStateCache.set(role, state);
    console.log(`📁 Loaded authentication state for ${role}`);
    return state;
  } catch (error) {
    console.warn(`⚠️  Failed to load auth state for ${role}:`, error);
    return null;
  }
}

/**
 * Authenticate as a role, using cached state if available
 */
export async function authenticateAs(
  context: BrowserContext, 
  page: Page, 
  role: Role, 
  baseURL: string | undefined,
  forceLogin: boolean = false
): Promise<void> {
  // If not forcing login, try to use existing auth state
  if (!forceLogin && isAuthStateValid(role)) {
    const authState = loadAuthState(role);
    if (authState) {
      try {
        // Apply the stored authentication state
        await context.addCookies(authState.cookies);
        
        // Restore localStorage from the auth state
        for (const origin of authState.origins) {
          await context.addInitScript((localStorage) => {
            for (const item of localStorage) {
              window.localStorage.setItem(item.name, item.value);
            }
          }, origin.localStorage);
        }
        
        // Navigate to the role's home page to verify auth
        const homePaths = {
          [Role.Admin]: '/trq/admin/home',
          [Role.ClinicAdmin]: '/trq/clinic-admins/home',
          [Role.Doctor]: '/trq/doctors/home',
          [Role.Client]: '/trq/clients/home',
          [Role.Patient]: '/trq/patients/home'
        };
        
        await page.goto(homePaths[role], { waitUntil: 'domcontentloaded', timeout: 10000 });
        
        // Check if we're actually authenticated
        const currentUrl = page.url();
        if (currentUrl.includes(homePaths[role])) {
          console.log(`✅ Successfully reused authentication for ${role}`);
          return;
        } else {
          console.log(`⚠️  Auth state invalid for ${role}, performing fresh login`);
        }
      } catch (error) {
        console.log(`⚠️  Failed to reuse auth state for ${role}, performing fresh login:`, error);
      }
    }
  }
  
  // Perform fresh login
  console.log(`🔐 Performing fresh authentication for ${role}...`);
  await loginAs(page, role, baseURL);
  
  // Save the new authentication state
  await saveAuthState(context, role);
}

/**
 * Clear all stored authentication states
 */
export function clearAuthStates(): void {
  if (fs.existsSync(AUTH_STATES_DIR)) {
    const files = fs.readdirSync(AUTH_STATES_DIR);
    files.forEach(file => {
      if (file.endsWith('-auth.json')) {
        fs.unlinkSync(path.join(AUTH_STATES_DIR, file));
      }
    });
  }
  authStateCache.clear();
  console.log('🧹 Cleared all authentication states');
}

/**
 * Clear authentication state for a specific role
 */
export function clearAuthState(role: Role): void {
  const authStatePath = getAuthStatePath(role);
  if (fs.existsSync(authStatePath)) {
    fs.unlinkSync(authStatePath);
  }
  authStateCache.delete(role);
  console.log(`🧹 Cleared authentication state for ${role}`);
}

/**
 * Check if a page is currently authenticated as the expected role
 */
export async function isAuthenticatedAs(page: Page, role: Role): Promise<boolean> {
  const currentUrl = page.url();
  const expectedPaths = {
    [Role.Admin]: '/trq/admin/home',
    [Role.ClinicAdmin]: '/trq/clinic-admins/home',
    [Role.Doctor]: '/trq/doctors/home',
    [Role.Client]: '/trq/clients/home',
    [Role.Patient]: '/trq/patients/home'
  };
  
  return currentUrl.includes(expectedPaths[role]);
}

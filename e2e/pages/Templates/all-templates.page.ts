import { Page, Locator, expect } from '@playwright/test';

export class AllTemplatesPage {
  readonly page: Page;
  readonly createTemplateButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.createTemplateButton = page.getByRole('button', { name: 'Create Template' });
  }

  async goto() {
    await this.page.goto('/trq/templates');
  }

  async clickCreateTemplate() {
    await this.createTemplateButton.click();
  }

  async waitForPageLoad() {
    await this.page.waitForLoadState('domcontentloaded');
  }
}
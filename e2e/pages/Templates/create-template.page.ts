import { Page, Locator } from '@playwright/test';

export class CreateTemplatePage {
  readonly page: Page;
  readonly nameInput: Locator;
  readonly descriptionInput: Locator;
  readonly addQuestionButton: Locator;
  readonly saveButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.nameInput = page.getByLabel('Name');
    this.descriptionInput = page.getByLabel('Description');
    this.addQuestionButton = page.getByRole('button', { name: 'Add Question' });
    this.saveButton = page.getByRole('button', { name: 'Save' });
  }

  async waitForPageLoad() {
    await this.page.waitForLoadState('domcontentloaded');
  }
}
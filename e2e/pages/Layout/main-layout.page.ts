import { Page, Locator, expect } from '@playwright/test';

export class MainLayoutPage {
  readonly page: Page;
  readonly sidebar: Locator;

  constructor(page: Page) {
    this.page = page;
    // Try multiple common sidebar selectors
    this.sidebar = page.locator('nav, [role="navigation"], .sidebar, .drawer, .MuiDrawer-root, aside').first();
  }

  getNavItem(itemId: string): Locator {
    // Map of test IDs to actual text content or selectors
    const navItemMap: { [key: string]: string } = {
      'admin-home': 'Home',
      'trq-settings': 'Settings', 
      'clinics': 'Clinics',
      'purchases-list': 'Purchases',
      'ai-chat': 'AI Chat Assist',
      'real-time-chat': 'Chat & Messaging',
      'questionnaires': 'Questionnaires',
      'templates': 'Templates',
      'all-products': 'All Products',
      'all-patients': 'Patients',
      'clients': 'Clients',
      'doctors': 'Doctors',
      'clinic-admins': 'Clinic Admins',
      'all-users': 'All Users',
      'user-migration': 'User Migration',
      'roles': 'Rule Manage',
      'permissions': 'Permissions',
      'site-analytics': 'Analytics'
    };
    
    const actualText = navItemMap[itemId] || itemId;
    
    // Try partial text match first (for potentially truncated text)
    let element = this.sidebar.locator(`text=${actualText}`).first();
    
    // If not found, try exact text match
    if (!element) {
      element = this.sidebar.locator(`text="${actualText}"`).first();
    }
    
    // If still not found, try href-based matching for certain items
    if (!element && itemId.includes('-')) {
      const hrefPart = itemId.replace('-', '/');
      element = this.sidebar.locator(`a[href*="${hrefPart}"]`).first();
    }
    
    return element;
  }

  async clickNavItem(itemId: string) {
    const navItem = this.getNavItem(itemId);
    await expect(navItem).toBeVisible();

    // Check if the item is inside a collapsed section (needs specific selectors based on NavCollapse structure)
    // This might involve checking parent elements or aria-expanded attributes.
    // For simplicity now, assuming direct click works or parent is already expanded.
    // TODO: Add logic to expand parent NavCollapse if necessary.

    await navItem.click();
  }

  async isNavItemVisible(itemId: string): Promise<boolean> {
    return this.getNavItem(itemId).isVisible();
  }

  // Helper to verify navigation and potentially wait for a key element on the target page
  async verifyNavigation(expectedUrl: string | RegExp, pageContentSelector?: string) {
    // Only check the path, not the host
    const currentPath = new URL(this.page.url()).pathname;
    if (typeof expectedUrl === 'string') {
      expect(currentPath).toBe(expectedUrl);
    } else {
      expect(currentPath).toMatch(expectedUrl);
    }
    if (pageContentSelector) {
      await expect(this.page.locator(pageContentSelector)).toBeVisible();
    }
  }

  // Opens all menu groups and collapses in the sidebar so all nav items are visible
  async openAllMenuGroups() {
    try {
      // Look for expandable navigation items (those with chevron/arrow indicators)
      // In the actual UI, these appear as clickable items with text like "Questionnaires", "Products", etc.
      const collapsibleItems = ['Questionnaires', 'Products', 'User Management', 'Admin'];
      
      for (const itemText of collapsibleItems) {
        try {
          // Find the collapsible item by text
          const item = this.sidebar.locator(`text="${itemText}"`).first();
          if (await item.isVisible()) {
            // Check if it has a sibling or child that indicates it's collapsed
            // We'll click it to expand, but use force in case of overlay issues
            await item.click({ force: true, timeout: 2000 });
            await this.page.waitForTimeout(500); // Brief wait for expansion
          }
        } catch (error) {
          // Continue with other items if one fails
          console.log(`Could not expand ${itemText}:`, error.message);
        }
      }
    } catch (error) {
      console.log('Error expanding menu groups:', error.message);
    }
  }
}

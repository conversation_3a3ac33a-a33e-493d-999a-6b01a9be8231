import { Page, Locator, expect } from '@playwright/test';

export class MainLayoutPage {
  readonly page: Page;
  readonly sidebar: Locator;

  constructor(page: Page) {
    this.page = page;
    // Try multiple common sidebar selectors
    this.sidebar = page.locator('nav, [role="navigation"], .sidebar, .drawer, .MuiDrawer-root, aside').first();
  }

  getNavItem(itemId: string): Locator {
    // Try multiple selector patterns for nav items within the sidebar
    const selectors = [
      `[data-testid="nav-item-${itemId}"]`,
      `[data-testid="${itemId}"]`,
      `[id="${itemId}"]`,
      `[aria-label*="${itemId}"]`,
      `a[href*="${itemId}"]`,
      `*:has-text("${itemId}")`
    ];
    
    // Check each selector within the sidebar
    for (const selector of selectors) {
      const element = this.sidebar.locator(selector);
      if (element) {
        return element.first();
      }
    }
    
    // Fallback to a simple text-based search
    return this.sidebar.locator(`text=${itemId}`).first();
  }

  async clickNavItem(itemId: string) {
    const navItem = this.getNavItem(itemId);
    await expect(navItem).toBeVisible();

    // Check if the item is inside a collapsed section (needs specific selectors based on NavCollapse structure)
    // This might involve checking parent elements or aria-expanded attributes.
    // For simplicity now, assuming direct click works or parent is already expanded.
    // TODO: Add logic to expand parent NavCollapse if necessary.

    await navItem.click();
  }

  async isNavItemVisible(itemId: string): Promise<boolean> {
    return this.getNavItem(itemId).isVisible();
  }

  // Helper to verify navigation and potentially wait for a key element on the target page
  async verifyNavigation(expectedUrl: string | RegExp, pageContentSelector?: string) {
    // Only check the path, not the host
    const currentPath = new URL(this.page.url()).pathname;
    if (typeof expectedUrl === 'string') {
      expect(currentPath).toBe(expectedUrl);
    } else {
      expect(currentPath).toMatch(expectedUrl);
    }
    if (pageContentSelector) {
      await expect(this.page.locator(pageContentSelector)).toBeVisible();
    }
  }

  // Opens all menu groups and collapses in the sidebar so all nav items are visible
  async openAllMenuGroups() {
    // Open all nav groups
    const navGroups = this.sidebar.locator('[data-testid^="nav-group-btn-"]');
    const groupCount = await navGroups.count();
    for (let i = 0; i < groupCount; i++) {
      const groupBtn = navGroups.nth(i);
      // Only click if not expanded (e.g. aria-expanded or class check if available)
      if (await groupBtn.isVisible()) {
        // Optionally check for expanded state if implemented
        await groupBtn.click({ force: true });
      }
    }
    // Open all collapses
    const navCollapses = this.sidebar.locator('[data-testid^="nav-collapse-btn-"]');
    const collapseCount = await navCollapses.count();
    for (let i = 0; i < collapseCount; i++) {
      const collapseBtn = navCollapses.nth(i);
      if (await collapseBtn.isVisible()) {
        await collapseBtn.click({ force: true });
      }
    }
  }
}

import { test, expect } from '@playwright/test';
import { createAuthStorageState, getStorageStatePath } from '../../utils/auth-utils';
import { Role } from '../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../pages/Templates/all-templates.page';

test.describe('Basic Template Navigation', () => {
  let allTemplatesPage: AllTemplatesPage;

  test.beforeAll(async ({ browser, baseURL }) => {
    // Create a single authenticated session for all tests
    const page = await browser.newPage();
    const storageStatePath = getStorageStatePath(Role.Admin);
    
    await createAuthStorageState(page, Role.Admin, storageStatePath, baseURL);
    await page.close();
  });

  test.use({ storageState: getStorageStatePath(Role.Admin) });

  test.beforeEach(async ({ page }) => {
    allTemplatesPage = new AllTemplatesPage(page);
  });

  test('should be able to navigate to templates page', async ({ page }) => {
    await allTemplatesPage.goto();
    
    // Check if we're on the templates page
    await expect(page).toHaveURL('/trq/templates');
    
    // Check if the create template button is visible
    await expect(allTemplatesPage.createTemplateButton).toBeVisible({ timeout: 10000 });
    
    console.log('Templates page loaded successfully');
  });

  test('should be able to click create template button', async ({ page }) => {
    await allTemplatesPage.goto();
    await allTemplatesPage.clickCreateTemplate();
    
    // Check if we're on the create template page
    await expect(page).toHaveURL('/trq/templates/add');
    
    console.log('Create template page loaded successfully');
  });
});

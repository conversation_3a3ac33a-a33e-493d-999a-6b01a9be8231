import { test, expect } from '@playwright/test';
import { Role } from '../../../src/[types]/Role';
import { AllTemplatesPage } from '../../pages/Templates/all-templates.page';

test.describe('Basic Template Navigation', () => {
  let allTemplatesPage: AllTemplatesPage;

  test.beforeEach(async ({ page, baseURL }) => {
    allTemplatesPage = new AllTemplatesPage(page);
    
    // Use direct login approach since auth state manager has issues
    const { loginAs } = await import('../../utils/auth-utils');
    await loginAs(page, Role.Admin, baseURL);
  });

  test('should be able to navigate to templates page', async ({ page }) => {
    await allTemplatesPage.goto();
    
    // Check if we're on the templates page
    await expect(page).toHaveURL('/trq/templates');
    
    // Check if the create template button is visible
    await expect(allTemplatesPage.createTemplateButton).toBeVisible({ timeout: 10000 });
    
    console.log('Templates page loaded successfully');
  });

  test('should be able to click create template button', async ({ page }) => {
    await allTemplatesPage.goto();
    await allTemplatesPage.clickCreateTemplate();
    
    // Check if we're on the create template page
    await expect(page).toHaveURL('/trq/templates/add');
    
    console.log('Create template page loaded successfully');
  });
});

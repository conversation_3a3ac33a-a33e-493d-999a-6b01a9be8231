import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../../pages/Templates/all-templates.page';
import { TemplateDetailsPage } from '../../../pages/Templates/template-details.page';

test.describe('Client Template Publishing Operations', () => {
  let allTemplatesPage: AllTemplatesPage;
  let templateDetailsPage: TemplateDetailsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    // Initialize page objects
    allTemplatesPage = new AllTemplatesPage(page);
    templateDetailsPage = new TemplateDetailsPage(page);

    // Login as client before each test
    await loginAs(page, Role.Client, baseURL);
  });

  test('PUBLISH: Client should NOT be able to publish templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify publish controls are not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have publish/unpublish toggle
      const publishToggleExists = await templateDetailsPage.isPublishToggleVisible();
      expect(publishToggleExists).toBe(false);
    }
  });

  test('UNPUBLISH: Client should NOT be able to unpublish templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify unpublish controls are not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have unpublish functionality
      const unpublishControlExists = await templateDetailsPage.isUnpublishControlVisible();
      expect(unpublishControlExists).toBe(false);
    }
  });

  test('PUBLISHING STATUS: Client should only see published, client-accessible templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should only see published templates that are client-accessible
    const onlyClientAccessiblePublishedVisible = await allTemplatesPage.areOnlyClientAccessiblePublishedTemplatesVisible();
    expect(onlyClientAccessiblePublishedVisible).toBe(true);
    
    // Should not see draft templates
    const draftTemplatesVisible = await allTemplatesPage.areDraftTemplatesVisible();
    expect(draftTemplatesVisible).toBe(false);
    
    // Should not see admin-only published templates
    const adminOnlyPublishedVisible = await allTemplatesPage.areAdminOnlyPublishedTemplatesVisible();
    expect(adminOnlyPublishedVisible).toBe(false);
  });

  test('TEMPLATE DETAILS: Client should see completion-focused template information', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify they are in client completion mode
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should be in client completion mode
      const isClientCompletionMode = await templateDetailsPage.isClientCompletionMode();
      expect(isClientCompletionMode).toBe(true);
      
      // Should see template information relevant to clients
      const clientRelevantInfoVisible = await templateDetailsPage.isClientRelevantInfoVisible();
      expect(clientRelevantInfoVisible).toBe(true);
      
      // Should not see publishing controls
      const publishingControlsVisible = await templateDetailsPage.arePublishingControlsVisible();
      expect(publishingControlsVisible).toBe(false);
      
      // Should not see administrative metadata
      const adminMetadataVisible = await templateDetailsPage.isAdminMetadataVisible();
      expect(adminMetadataVisible).toBe(false);
    }
  });

  test('PERMISSIONS: Client should have no publishing permissions', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Verify no publishing actions are available
    const publishingActionsDisabled = await allTemplatesPage.arePublishingActionsDisabled();
    expect(publishingActionsDisabled).toBe(true);
    
    // Verify no template management actions are available
    const managementActionsDisabled = await allTemplatesPage.areManagementActionsDisabled();
    expect(managementActionsDisabled).toBe(true);
    
    // Verify only completion actions are available
    const completionActionsAvailable = await allTemplatesPage.areCompletionActionsAvailable();
    expect(completionActionsAvailable).toBe(true);
  });

  test('TEMPLATE COMPLETION: Client should be able to start/complete available templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify completion functionality
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should have completion button
      const completeButtonExists = await templateDetailsPage.isCompleteTemplateButtonVisible();
      expect(completeButtonExists).toBe(true);
      
      // Should be able to start template completion
      const canStartCompletion = await templateDetailsPage.canStartTemplateCompletion();
      expect(canStartCompletion).toBe(true);
      
      // Should not have administrative completion controls
      const adminCompletionControlsVisible = await templateDetailsPage.areAdminCompletionControlsVisible();
      expect(adminCompletionControlsVisible).toBe(false);
    }
  });

  test('NAVIGATION: Client should be able to navigate templates in completion mode', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to navigate to templates
    await allTemplatesPage.waitForPageLoad();
    
    // Should be able to view template details in completion mode
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should reach template completion page
      await templateDetailsPage.waitForPageLoad();
      
      // Should be in client completion mode
      const isCompletionMode = await templateDetailsPage.isCompletionMode();
      expect(isCompletionMode).toBe(true);
      
      // Should be able to navigate back
      await templateDetailsPage.navigateBack();
      await allTemplatesPage.waitForPageLoad();
    }
  });

  test('ACCESS CONTROL: Client should not access template management URLs', async ({ page }) => {
    // Try to navigate to create template page directly
    await page.goto('/trq/templates/create');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template edit page directly
    await page.goto('/trq/templates/edit/123');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template publishing page directly
    await page.goto('/trq/templates/publish/123');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template admin panel directly
    await page.goto('/trq/templates/admin');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
  });

  test('TEMPLATE FILTERING: Client should see filtered templates based on client access', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should only see client-appropriate templates
    const clientAppropriateTemplatesOnly = await allTemplatesPage.areOnlyClientAppropriateTemplatesVisible();
    expect(clientAppropriateTemplatesOnly).toBe(true);
    
    // Should not see clinical assessment templates
    const clinicalTemplatesHidden = await allTemplatesPage.areClinicalTemplatesHidden();
    expect(clinicalTemplatesHidden).toBe(true);
    
    // Should not see patient-specific templates
    const patientTemplatesHidden = await allTemplatesPage.arePatientTemplatesHidden();
    expect(patientTemplatesHidden).toBe(true);
    
    // Should not see administrative templates
    const adminTemplatesHidden = await allTemplatesPage.areAdminTemplatesHidden();
    expect(adminTemplatesHidden).toBe(true);
    
    // Should not see templates in draft state
    const draftTemplatesHidden = await allTemplatesPage.areDraftTemplatesHidden();
    expect(draftTemplatesHidden).toBe(true);
  });

  test('TEMPLATE CATEGORIZATION: Client should see appropriate template categories', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should see client-relevant categories in filter
    const clientRelevantCategoriesVisible = await allTemplatesPage.areClientRelevantCategoriesVisible();
    expect(clientRelevantCategoriesVisible).toBe(true);
    
    // Should not see admin-only categories
    const adminOnlyCategoriesHidden = await allTemplatesPage.areAdminOnlyCategoriesHidden();
    expect(adminOnlyCategoriesHidden).toBe(true);
    
    // Should not see clinical-only categories
    const clinicalOnlyCategoriesHidden = await allTemplatesPage.areClinicalOnlyCategoriesHidden();
    expect(clinicalOnlyCategoriesHidden).toBe(true);
    
    // Should not see patient-only categories
    const patientOnlyCategoriesHidden = await allTemplatesPage.arePatientOnlyCategoriesHidden();
    expect(patientOnlyCategoriesHidden).toBe(true);
  });

  test('ORGANIZATION TEMPLATES: Client should see organization-specific templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should see templates assigned to client's organization
    const organizationTemplatesVisible = await allTemplatesPage.areOrganizationTemplatesVisible();
    expect(organizationTemplatesVisible).toBe(true);
    
    // Should not see templates from other organizations (unless public)
    const otherOrgPrivateTemplatesVisible = await allTemplatesPage.areOtherOrganizationPrivateTemplatesVisible();
    expect(otherOrgPrivateTemplatesVisible).toBe(false);
    
    // Should see public/general templates
    const publicTemplatesVisible = await allTemplatesPage.arePublicTemplatesVisible();
    expect(publicTemplatesVisible).toBe(true);
  });

  test('TEMPLATE ASSIGNMENT: Client should see properly assigned templates based on role', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should see templates assigned to client role
    const clientRoleTemplatesVisible = await allTemplatesPage.areClientRoleTemplatesVisible();
    expect(clientRoleTemplatesVisible).toBe(true);
    
    // Should not see role-specific templates for other roles
    const otherRoleTemplatesVisible = await allTemplatesPage.areOtherRoleTemplatesVisible();
    expect(otherRoleTemplatesVisible).toBe(false);
    
    // Should see general/multi-role templates
    const generalRoleTemplatesVisible = await allTemplatesPage.areGeneralRoleTemplatesVisible();
    expect(generalRoleTemplatesVisible).toBe(true);
  });
});
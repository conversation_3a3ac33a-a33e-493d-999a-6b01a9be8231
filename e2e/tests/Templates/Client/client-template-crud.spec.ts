import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../../pages/Templates/all-templates.page';
import { CreateTemplatePage } from '../../../pages/Templates/create-template.page';
import { TemplateDetailsPage } from '../../../pages/Templates/template-details.page';

test.describe('Client Template CRUD Operations', () => {
  let allTemplatesPage: AllTemplatesPage;
  let createTemplatePage: CreateTemplatePage;
  let templateDetailsPage: TemplateDetailsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    // Initialize page objects
    allTemplatesPage = new AllTemplatesPage(page);
    createTemplatePage = new CreateTemplatePage(page);
    templateDetailsPage = new TemplateDetailsPage(page);

    // Login as client before each test
    await loginAs(page, Role.Client, baseURL);
  });

  test('CREATE: Client should NOT be able to create templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Verify create template button is not visible/accessible
    const createButtonExists = await allTemplatesPage.isCreateTemplateButtonVisible();
    expect(createButtonExists).toBe(false);
    
    // Try to directly navigate to create template page
    await page.goto('/trq/templates/create');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
  });

  test('READ: Client should be able to view published templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to see templates list
    await allTemplatesPage.waitForPageLoad();
    
    // Should only see published templates that are client-accessible
    const clientAccessibleTemplatesVisible = await allTemplatesPage.areClientAccessibleTemplatesVisible();
    expect(clientAccessibleTemplatesVisible).toBe(true);
    
    // Should not see draft templates
    const draftTemplatesVisible = await allTemplatesPage.areDraftTemplatesVisible();
    expect(draftTemplatesVisible).toBe(false);
    
    // Should not see admin-only templates
    const adminOnlyTemplatesVisible = await allTemplatesPage.areAdminOnlyTemplatesVisible();
    expect(adminOnlyTemplatesVisible).toBe(false);
  });

  test('UPDATE: Client should NOT be able to edit templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should not see edit buttons on any templates
    const editButtonsVisible = await allTemplatesPage.areEditButtonsVisible();
    expect(editButtonsVisible).toBe(false);
    
    // If there are templates, verify clicking them only shows read-only view
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have edit button in template details
      const editButtonExists = await templateDetailsPage.isEditButtonVisible();
      expect(editButtonExists).toBe(false);
    }
  });

  test('DELETE: Client should NOT be able to delete templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should not see delete buttons on any templates
    const deleteButtonsVisible = await allTemplatesPage.areDeleteButtonsVisible();
    expect(deleteButtonsVisible).toBe(false);
    
    // If there are templates, verify delete action is not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have delete button in template details
      const deleteButtonExists = await templateDetailsPage.isDeleteButtonVisible();
      expect(deleteButtonExists).toBe(false);
    }
  });

  test('PUBLISH: Client should NOT be able to publish/unpublish templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify publish controls are not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have publish/unpublish toggle
      const publishToggleExists = await templateDetailsPage.isPublishToggleVisible();
      expect(publishToggleExists).toBe(false);
    }
  });

  test('SEARCH: Client should be able to search client-accessible templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to use search functionality
    const searchBoxExists = await allTemplatesPage.isSearchBoxVisible();
    expect(searchBoxExists).toBe(true);
    
    // Should be able to search templates
    await allTemplatesPage.searchTemplates('survey');
    
    // Should see search results (if any exist)
    await page.waitForTimeout(1000);
    const searchResultsVisible = await allTemplatesPage.areSearchResultsVisible();
    expect(searchResultsVisible).toBe(true);
  });

  test('FILTER: Client should be able to filter templates by client-relevant categories', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to use category filter
    const filterExists = await allTemplatesPage.isCategoryFilterVisible();
    expect(filterExists).toBe(true);
    
    // Should be able to filter by client-relevant categories
    await allTemplatesPage.selectCategoryFilter('survey');
    await page.waitForTimeout(1000);
    
    // Should see filtered results
    const filteredResultsVisible = await allTemplatesPage.areFilteredResultsVisible();
    expect(filteredResultsVisible).toBe(true);
  });

  test('TEMPLATE COMPLETION: Client should be able to complete available templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify completion action is available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should have "Complete Template" or "Start Template" button
      const completeButtonExists = await templateDetailsPage.isCompleteTemplateButtonVisible();
      expect(completeButtonExists).toBe(true);
      
      // Should not have administrative actions
      const adminActionsVisible = await templateDetailsPage.areAdminActionsVisible();
      expect(adminActionsVisible).toBe(false);
    }
  });

  test('PERMISSIONS: Client should only have read and completion access', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Verify all management actions are disabled
    const managementActionsDisabled = await allTemplatesPage.areManagementActionsDisabled();
    expect(managementActionsDisabled).toBe(true);
    
    // Verify only client-accessible templates are visible
    const onlyClientAccessibleVisible = await allTemplatesPage.areOnlyClientAccessibleTemplatesVisible();
    expect(onlyClientAccessibleVisible).toBe(true);
    
    // Verify read-only access to template details
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should be in read-only mode with completion option
      const isClientMode = await templateDetailsPage.isClientMode();
      expect(isClientMode).toBe(true);
    }
  });

  test('ACCESS CONTROL: Client should not access restricted template management URLs', async ({ page }) => {
    // Try to navigate to create template page directly
    await page.goto('/trq/templates/create');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template edit page directly
    await page.goto('/trq/templates/edit/123');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template admin page directly
    await page.goto('/trq/templates/admin');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
  });

  test('TEMPLATE VISIBILITY: Client should only see templates marked for client use', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should only see templates appropriate for clients
    const clientTemplatesOnly = await allTemplatesPage.areOnlyClientTemplatesVisible();
    expect(clientTemplatesOnly).toBe(true);
    
    // Should not see clinical assessment templates
    const clinicalTemplatesVisible = await allTemplatesPage.areClinicalTemplatesVisible();
    expect(clinicalTemplatesVisible).toBe(false);
    
    // Should not see patient-specific templates
    const patientTemplatesVisible = await allTemplatesPage.arePatientTemplatesVisible();
    expect(patientTemplatesVisible).toBe(false);
    
    // Should not see admin templates
    const adminTemplatesVisible = await allTemplatesPage.areAdminTemplatesVisible();
    expect(adminTemplatesVisible).toBe(false);
  });

  test('TEMPLATE ASSIGNMENT: Client should see templates assigned to their organization', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should see templates assigned to client's organization
    const organizationTemplatesVisible = await allTemplatesPage.areOrganizationTemplatesVisible();
    expect(organizationTemplatesVisible).toBe(true);
    
    // Should not see templates from other organizations
    const otherOrgTemplatesVisible = await allTemplatesPage.areOtherOrganizationTemplatesVisible();
    expect(otherOrgTemplatesVisible).toBe(false);
    
    // Should see general/public templates
    const publicTemplatesVisible = await allTemplatesPage.arePublicTemplatesVisible();
    expect(publicTemplatesVisible).toBe(true);
  });

  test('TEMPLATE COMPLETION HISTORY: Client should see their template completion history', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify completion history is available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should see completion history section
      const completionHistoryVisible = await templateDetailsPage.isCompletionHistoryVisible();
      expect(completionHistoryVisible).toBe(true);
      
      // Should not see other users' completion history
      const otherUsersHistoryVisible = await templateDetailsPage.isOtherUsersHistoryVisible();
      expect(otherUsersHistoryVisible).toBe(false);
    }
  });
});
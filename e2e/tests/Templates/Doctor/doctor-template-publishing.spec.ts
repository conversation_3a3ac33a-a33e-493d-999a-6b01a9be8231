import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../../pages/Templates/all-templates.page';
import { TemplateDetailsPage } from '../../../pages/Templates/template-details.page';

test.describe('Doctor Template Publishing Operations', () => {
  let allTemplatesPage: AllTemplatesPage;
  let templateDetailsPage: TemplateDetailsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    // Initialize page objects
    allTemplatesPage = new AllTemplatesPage(page);
    templateDetailsPage = new TemplateDetailsPage(page);

    // Login as doctor before each test
    await loginAs(page, Role.Doctor, baseURL);
  });

  test('PUBLISH: Doctor should NOT be able to publish templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify publish controls are not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have publish/unpublish toggle
      const publishToggleExists = await templateDetailsPage.isPublishToggleVisible();
      expect(publishToggleExists).toBe(false);
    }
  });

  test('UNPUBLISH: Doctor should NOT be able to unpublish templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are published templates, verify unpublish controls are not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have unpublish functionality
      const unpublishControlExists = await templateDetailsPage.isUnpublishControlVisible();
      expect(unpublishControlExists).toBe(false);
    }
  });

  test('PUBLISHING STATUS: Doctor should only see published templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should only see published templates
    const onlyPublishedVisible = await allTemplatesPage.areOnlyPublishedTemplatesVisible();
    expect(onlyPublishedVisible).toBe(true);
    
    // Should not see draft templates
    const draftTemplatesVisible = await allTemplatesPage.areDraftTemplatesVisible();
    expect(draftTemplatesVisible).toBe(false);
  });

  test('TEMPLATE DETAILS: Doctor should see read-only template information', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify they are in read-only mode
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should be in read-only mode
      const isReadOnlyMode = await templateDetailsPage.isReadOnlyMode();
      expect(isReadOnlyMode).toBe(true);
      
      // Should see template information but no editing controls
      const templateInfoVisible = await templateDetailsPage.isTemplateInfoVisible();
      expect(templateInfoVisible).toBe(true);
      
      // Should not see editing controls
      const editingControlsVisible = await templateDetailsPage.areEditingControlsVisible();
      expect(editingControlsVisible).toBe(false);
    }
  });

  test('PERMISSIONS: Doctor should have no publishing permissions', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Verify no publishing actions are available
    const publishingActionsDisabled = await allTemplatesPage.arePublishingActionsDisabled();
    expect(publishingActionsDisabled).toBe(true);
    
    // Verify no template management actions are available
    const managementActionsDisabled = await allTemplatesPage.areManagementActionsDisabled();
    expect(managementActionsDisabled).toBe(true);
  });

  test('NAVIGATION: Doctor should be able to navigate templates but not modify them', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to navigate to templates
    await allTemplatesPage.waitForPageLoad();
    
    // Should be able to view template details
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should reach template details page
      await templateDetailsPage.waitForPageLoad();
      
      // Should be able to navigate back
      await templateDetailsPage.navigateBack();
      await allTemplatesPage.waitForPageLoad();
    }
  });

  test('ACCESS CONTROL: Doctor should not access unpublished template URLs directly', async ({ page }) => {
    // Try to navigate to create template page directly
    await page.goto('/trq/templates/create');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template edit page directly (if we had an ID)
    // This would typically require a specific template ID, so we'll test the general pattern
    await page.goto('/trq/templates/edit/123');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
  });
});
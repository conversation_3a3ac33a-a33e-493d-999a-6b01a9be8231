import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../../pages/Templates/all-templates.page';
import { CreateTemplatePage } from '../../../pages/Templates/create-template.page';
import { TemplateDetailsPage } from '../../../pages/Templates/template-details.page';

test.describe('Doctor Template CRUD Operations', () => {
  let allTemplatesPage: AllTemplatesPage;
  let createTemplatePage: CreateTemplatePage;
  let templateDetailsPage: TemplateDetailsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    // Initialize page objects
    allTemplatesPage = new AllTemplatesPage(page);
    createTemplatePage = new CreateTemplatePage(page);
    templateDetailsPage = new TemplateDetailsPage(page);

    // Login as doctor before each test
    await loginAs(page, Role.Doctor, baseURL);
  });

  test('CREATE: Doctor should NOT be able to create templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Verify create template button is not visible/accessible
    const createButtonExists = await allTemplatesPage.isCreateTemplateButtonVisible();
    expect(createButtonExists).toBe(false);
    
    // Try to directly navigate to create template page
    await page.goto('/trq/templates/create');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
  });

  test('READ: Doctor should be able to view published templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to see templates list
    await allTemplatesPage.waitForPageLoad();
    
    // Should only see published templates, not drafts
    const publishedTemplatesVisible = await allTemplatesPage.arePublishedTemplatesVisible();
    expect(publishedTemplatesVisible).toBe(true);
    
    // Should not see draft templates
    const draftTemplatesVisible = await allTemplatesPage.areDraftTemplatesVisible();
    expect(draftTemplatesVisible).toBe(false);
  });

  test('UPDATE: Doctor should NOT be able to edit templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should not see edit buttons on any templates
    const editButtonsVisible = await allTemplatesPage.areEditButtonsVisible();
    expect(editButtonsVisible).toBe(false);
    
    // If there are templates, verify clicking them only shows read-only view
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have edit button in template details
      const editButtonExists = await templateDetailsPage.isEditButtonVisible();
      expect(editButtonExists).toBe(false);
    }
  });

  test('DELETE: Doctor should NOT be able to delete templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should not see delete buttons on any templates
    const deleteButtonsVisible = await allTemplatesPage.areDeleteButtonsVisible();
    expect(deleteButtonsVisible).toBe(false);
    
    // If there are templates, verify delete action is not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have delete button in template details
      const deleteButtonExists = await templateDetailsPage.isDeleteButtonVisible();
      expect(deleteButtonExists).toBe(false);
    }
  });

  test('PUBLISH: Doctor should NOT be able to publish/unpublish templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify publish controls are not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have publish/unpublish toggle
      const publishToggleExists = await templateDetailsPage.isPublishToggleVisible();
      expect(publishToggleExists).toBe(false);
    }
  });

  test('SEARCH: Doctor should be able to search published templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to use search functionality
    const searchBoxExists = await allTemplatesPage.isSearchBoxVisible();
    expect(searchBoxExists).toBe(true);
    
    // Should be able to search templates
    await allTemplatesPage.searchTemplates('medical');
    
    // Should see search results (if any exist)
    await page.waitForTimeout(1000);
    const searchResultsVisible = await allTemplatesPage.areSearchResultsVisible();
    expect(searchResultsVisible).toBe(true);
  });

  test('FILTER: Doctor should be able to filter templates by category', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to use category filter
    const filterExists = await allTemplatesPage.isCategoryFilterVisible();
    expect(filterExists).toBe(true);
    
    // Should be able to filter by category
    await allTemplatesPage.selectCategoryFilter('medical');
    await page.waitForTimeout(1000);
    
    // Should see filtered results
    const filteredResultsVisible = await allTemplatesPage.areFilteredResultsVisible();
    expect(filteredResultsVisible).toBe(true);
  });

  test('PERMISSIONS: Doctor should only have read access to published templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Verify all management actions are disabled
    const managementActionsDisabled = await allTemplatesPage.areManagementActionsDisabled();
    expect(managementActionsDisabled).toBe(true);
    
    // Verify only published templates are visible
    const onlyPublishedVisible = await allTemplatesPage.areOnlyPublishedTemplatesVisible();
    expect(onlyPublishedVisible).toBe(true);
    
    // Verify read-only access to template details
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should be in read-only mode
      const isReadOnlyMode = await templateDetailsPage.isReadOnlyMode();
      expect(isReadOnlyMode).toBe(true);
    }
  });
});
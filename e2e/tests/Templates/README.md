# Template E2E Tests

This directory contains comprehensive end-to-end tests for template CRUD operations, publishing workflows, and management for both Admin and Clinic Admin users.

## Overview

These tests cover the complete workflow of:
1. **CRUD Operations**: Creating, Reading, Updating, and Deleting questionnaire templates
2. **Publishing Workflows**: Publishing templates to make them available as products
3. **Product Integration**: Verifying published templates appear in the products marketplace
4. **Template Lifecycle**: Managing template lifecycle (publish/unpublish)
5. **Role-based Access**: Testing role-based access control for template management
6. **Search and Filtering**: Testing template search and category filtering functionality

## Test Structure

```
e2e/tests/Templates/
├── Admin/
│   ├── admin-template-crud.spec.ts
│   └── admin-template-publishing.spec.ts
├── ClinicAdmin/
│   ├── clinic-admin-template-crud.spec.ts
│   └── clinic-admin-template-publishing.spec.ts
├── Doctor/
│   ├── doctor-template-crud.spec.ts
│   └── doctor-template-publishing.spec.ts
├── Patient/
│   ├── patient-template-crud.spec.ts
│   └── patient-template-publishing.spec.ts
├── Client/
│   ├── client-template-crud.spec.ts
│   └── client-template-publishing.spec.ts
└── README.md (this file)
```

## Page Object Models

The tests use the following page object models located in `e2e/pages/Templates/`:

- `AllTemplatesPage` - Template listing and management page
- `CreateTemplatePage` - Template creation and editing forms
- `TemplateDetailsPage` - Template details view with publish/unpublish functionality

Additional page objects used:
- `ProductsPage` - Products marketplace for verifying published templates

## Test Scenarios

### Admin Template CRUD Tests (`admin-template-crud.spec.ts`)

1. **CREATE: Template Creation**
   - Admin creates new templates with various question types
   - Validates template creation with proper form data
   - Verifies templates appear in draft state initially

2. **READ: Template Viewing**
   - Admin views template details and metadata
   - Verifies template questions and structure
   - Checks template publication status

3. **UPDATE: Template Editing**
   - Admin edits existing template details
   - Adds/removes questions from templates
   - Updates template metadata and categories

4. **DELETE: Template Removal**
   - Admin deletes templates with confirmation
   - Verifies templates are removed from the system
   - Tests cleanup and data integrity

5. **CRUD WORKFLOW: Complete Lifecycle**
   - End-to-end template lifecycle testing
   - Create → Read → Update → Publish → Unpublish → Delete
   - Validates all operations work together seamlessly

### Admin Template Publishing Tests (`admin-template-publishing.spec.ts`)

1. **Create and Publish Template**
   - Admin creates a new template with questions
   - Publishes the template
   - Verifies template appears as a product in marketplace

2. **Unpublish Template**
   - Creates and publishes a template
   - Unpublishes the template
   - Verifies product disappears from marketplace

3. **Template Copy and Publish**
   - Creates an original template
   - Creates a modified copy
   - Publishes only the copy
   - Verifies only published template appears as product

4. **Edit Restrictions for Published Templates**
   - Creates and publishes a template
   - Verifies edit functionality is disabled for published templates
   - Unpublishes and verifies edit functionality returns

### Clinic Admin Template CRUD Tests (`clinic-admin-template-crud.spec.ts`)

1. **CREATE: Clinical Template Creation**
   - Clinic admin creates clinical assessment templates
   - Creates patient intake templates with comprehensive questions
   - Validates clinical-specific template categories

2. **READ: Clinical Template Viewing**
   - Views clinical template details and assessments
   - Verifies clinical question structures
   - Checks template metadata for clinical use

3. **UPDATE: Clinical Template Editing**
   - Edits clinical assessment templates
   - Updates patient intake workflows
   - Modifies clinical question sets

4. **DELETE: Clinical Template Removal**
   - Deletes clinical templates with proper cleanup
   - Verifies removal from clinical workflows
   - Tests data integrity for clinical data

5. **SEARCH AND FILTER: Template Management**
   - Searches clinical templates by name/category
   - Filters templates by clinical categories
   - Tests template organization and discovery

6. **CRUD WORKFLOW: Clinical Template Lifecycle**
   - Complete clinical template lifecycle testing
   - Clinical-specific publish/unpublish workflows
   - Integration with clinical assessment systems

### Clinic Admin Template Publishing Tests (`clinic-admin-template-publishing.spec.ts`)

1. **Clinic Admin Template Creation and Publishing**
   - Clinic admin creates clinic-specific templates
   - Publishes templates with clinical assessment questions
   - Verifies templates appear as products

2. **Clinic-Specific Template Management**
   - Creates templates with clinic workflow questions
   - Manages clinic-specific template categories
   - Verifies clinic admin access to their templates

3. **Patient Intake Template Creation**
   - Creates comprehensive patient intake templates
   - Adds intake-specific questions
   - Publishes and verifies as products

4. **Republish Workflow**
   - Tests unpublish and republish functionality
   - Verifies product availability changes accordingly

5. **Permission Verification**
   - Verifies clinic admin has same publishing permissions as admin
   - Tests template management capabilities

### Doctor Template Tests (`doctor-template-crud.spec.ts` & `doctor-template-publishing.spec.ts`)

1. **READ-ONLY ACCESS: Template Viewing**
   - Doctor views published templates only
   - Verifies no access to draft templates
   - Confirms read-only mode for all templates

2. **PERMISSION RESTRICTIONS: No Management Access**
   - Verifies doctors cannot create, edit, or delete templates
   - Confirms no access to publishing controls
   - Tests search and filter functionality (read-only)

3. **ACCESS CONTROL: URL Protection**
   - Verifies doctors cannot access template management URLs directly
   - Tests redirection to appropriate pages or error messages

### Patient Template Tests (`patient-template-crud.spec.ts` & `patient-template-publishing.spec.ts`)

1. **READ-ONLY ACCESS: Patient-Appropriate Templates**
   - Patient views only published, patient-accessible templates
   - Verifies no access to clinical or admin templates
   - Confirms template completion functionality

2. **PERMISSION RESTRICTIONS: No Management Access**
   - Verifies patients cannot create, edit, or delete templates
   - Confirms no access to publishing controls
   - Tests patient-specific search and filtering

3. **TEMPLATE COMPLETION: Patient Workflow**
   - Verifies patients can complete available templates
   - Tests completion history and tracking
   - Confirms patient-focused template information display

4. **ACCESS CONTROL: Role-Based Filtering**
   - Verifies patients only see appropriate template categories
   - Tests filtering based on patient access levels
   - Confirms no access to restricted template types

### Client Template Tests (`client-template-crud.spec.ts` & `client-template-publishing.spec.ts`)

1. **READ-ONLY ACCESS: Client-Appropriate Templates**
   - Client views only published, client-accessible templates
   - Verifies access to organization-specific templates
   - Confirms template completion functionality

2. **PERMISSION RESTRICTIONS: No Management Access**
   - Verifies clients cannot create, edit, or delete templates
   - Confirms no access to publishing controls
   - Tests client-specific search and filtering

3. **TEMPLATE COMPLETION: Client Workflow**
   - Verifies clients can complete available templates
   - Tests organization-based template assignment
   - Confirms client-focused template information display

4. **ORGANIZATION FILTERING: Multi-Tenant Access**
   - Verifies clients see organization-specific templates
   - Tests filtering based on client organization
   - Confirms no access to other organization's private templates

## Running Tests

### Using npm scripts:

```bash
# Run all template tests (CRUD + Publishing)
npm run test:e2e:templates

# Run all CRUD tests for both roles
npm run test:e2e:templates:crud
npm run e2e:templates:crud

# Run admin template tests (all)
npm run test:e2e:templates:admin
npm run e2e:templates:admin

# Run admin CRUD tests only
npm run test:e2e:templates:admin:crud
npm run e2e:templates:admin:crud

# Run admin publishing tests only
npm run test:e2e:templates:admin:publishing

# Run clinic admin template tests (all)
npm run test:e2e:templates:clinic-admin
npm run e2e:templates:clinic-admin

# Run clinic admin CRUD tests only
npm run test:e2e:templates:clinic-admin:crud
npm run e2e:templates:clinic-admin:crud

# Run clinic admin publishing tests only
npm run test:e2e:templates:clinic-admin:publishing

# Run doctor template tests (all)
npm run test:e2e:templates:doctor
npm run e2e:templates:doctor

# Run doctor CRUD tests only
npm run test:e2e:templates:doctor:crud
npm run e2e:templates:doctor:crud

# Run doctor publishing tests only
npm run test:e2e:templates:doctor:publishing

# Run patient template tests (all)
npm run test:e2e:templates:patient
npm run e2e:templates:patient

# Run patient CRUD tests only
npm run test:e2e:templates:patient:crud
npm run e2e:templates:patient:crud

# Run patient publishing tests only
npm run test:e2e:templates:patient:publishing

# Run client template tests (all)
npm run test:e2e:templates:client
npm run e2e:templates:client

# Run client CRUD tests only
npm run test:e2e:templates:client:crud
npm run e2e:templates:client:crud

# Run client publishing tests only
npm run test:e2e:templates:client:publishing
```

### Using Playwright directly:

```bash
# Run all template tests
npx playwright test e2e/tests/Templates/

# Run all CRUD tests
npx playwright test e2e/tests/Templates/Admin/admin-template-crud.spec.ts e2e/tests/Templates/ClinicAdmin/clinic-admin-template-crud.spec.ts

# Run admin tests (all)
npx playwright test e2e/tests/Templates/Admin/

# Run admin CRUD tests only
npx playwright test e2e/tests/Templates/Admin/admin-template-crud.spec.ts

# Run admin publishing tests only
npx playwright test e2e/tests/Templates/Admin/admin-template-publishing.spec.ts

# Run clinic admin tests (all)
npx playwright test e2e/tests/Templates/ClinicAdmin/

# Run clinic admin CRUD tests only
npx playwright test e2e/tests/Templates/ClinicAdmin/clinic-admin-template-crud.spec.ts

# Run clinic admin publishing tests only
npx playwright test e2e/tests/Templates/ClinicAdmin/clinic-admin-template-publishing.spec.ts

# Run doctor tests (all)
npx playwright test e2e/tests/Templates/Doctor/

# Run doctor CRUD tests only
npx playwright test e2e/tests/Templates/Doctor/doctor-template-crud.spec.ts

# Run doctor publishing tests only
npx playwright test e2e/tests/Templates/Doctor/doctor-template-publishing.spec.ts

# Run patient tests (all)
npx playwright test e2e/tests/Templates/Patient/

# Run patient CRUD tests only
npx playwright test e2e/tests/Templates/Patient/patient-template-crud.spec.ts

# Run patient publishing tests only
npx playwright test e2e/tests/Templates/Patient/patient-template-publishing.spec.ts

# Run client tests (all)
npx playwright test e2e/tests/Templates/Client/

# Run client CRUD tests only
npx playwright test e2e/tests/Templates/Client/client-template-crud.spec.ts

# Run client publishing tests only
npx playwright test e2e/tests/Templates/Client/client-template-publishing.spec.ts

# Run specific test file
npx playwright test e2e/tests/Templates/Admin/admin-template-publishing.spec.ts

# Run with browser visible
npx playwright test e2e/tests/Templates/ --headed

# Run in debug mode
npx playwright test e2e/tests/Templates/ --debug
```

## Features

### CRUD Operations
- **Create**: Template creation with various question types and categories
- **Read**: Template viewing, details display, and metadata verification
- **Update**: Template editing, question management, and metadata updates
- **Delete**: Template removal with confirmation and cleanup verification

### Publishing Workflow
- **Publish/Unpublish**: Template publishing state management
- **Product Integration**: Verification of published templates in marketplace
- **State Validation**: Testing draft/published state transitions

### Role-based Testing
- **Admin Role**: Full template management capabilities (create, read, update, delete, publish)
- **Clinic Admin Role**: Clinical-specific template operations (create, read, update, delete, publish)
- **Doctor Role**: Read-only access to published templates
- **Patient Role**: Read-only access to patient-appropriate published templates, completion functionality
- **Client Role**: Read-only access to client-appropriate published templates, completion functionality
- **Permission Testing**: Role-based access control validation for all roles

### Data Management
- **Template Persistence**: Data integrity across operations
- **Question Management**: Adding, editing, and removing questions
- **Category Management**: Template categorization and filtering
- **Search Functionality**: Template discovery and filtering

### Quality Assurance
- **Automatic Cleanup**: Test data cleanup after each test
- **Error Handling**: Validation of error states and messages
- **UI Interaction**: Comprehensive UI component testing
- **Performance**: Optimized test execution with session reuse

## Test Data

Tests create unique templates using timestamps to avoid conflicts:
- Template names include timestamps: `E2E Admin Template ${Date.now()}`
- Each test creates its own test data
- Tests are designed to be independent and can run in parallel

## Performance Optimization

These tests are optimized for performance by:
- **Persistent Login**: Login happens once per test suite using `test.beforeAll()` and storage state
- **Session Reuse**: All tests in a suite reuse the same authenticated session
- **Reduced Setup Time**: No repeated login/logout cycles between tests
- **Parallel Execution**: Tests can run in parallel within the same role

## Key Workflows Tested

### Template Creation to Product Publication Flow

```
1. Login as Admin/Clinic Admin
2. Navigate to Templates page (/trq/templates)
3. Create new template with questions
4. Save template (appears as draft)
5. Navigate to template details
6. Publish template using toggle switch
7. Navigate to Products page (/trq/products)
8. Verify template appears as purchasable product
```

### Template Unpublishing Flow

```
1. Start with published template
2. Navigate to template details
3. Unpublish using toggle switch
4. Navigate to Products page
5. Verify product no longer appears
```

## Verification Points

- **Template Creation**: Template appears in templates list with correct status
- **Template Publishing**: Publish toggle works and status updates
- **Product Appearance**: Published templates appear as products in marketplace
- **Product Removal**: Unpublished templates disappear from marketplace
- **Role Permissions**: Both Admin and Clinic Admin can publish templates
- **Edit Restrictions**: Published templates cannot be edited
- **Template Details**: Correct template information is preserved

## Dependencies

- Playwright Test Framework
- Page Object Models in `e2e/pages/Templates/` and `e2e/pages/Products/`
- Auth utilities in `e2e/utils/auth-utils.ts`
- Role definitions from `src/RBAC/[types]/Role`

## Prerequisites

1. Test environment should be set up with:
   - Admin and Clinic Admin test users
   - Clean database state
   - Template and product services running

2. Run setup scripts if needed:
   ```bash
   npm run e2e:reset-all
   ```

## Troubleshooting

### Common Issues

1. **Template not appearing as product**: 
   - Check if template publishing service is running
   - Verify template has required fields filled
   - Check browser network tab for API errors

2. **Permission errors**:
   - Verify test user has correct role (Admin or ClinicAdmin)
   - Check RBAC configuration

3. **Timing issues**:
   - Tests include appropriate waits for async operations
   - Increase timeouts if needed for slower environments

### Debug Tips

- Use `--headed` flag to see browser actions
- Use `--debug` flag to step through tests
- Check browser console for JavaScript errors
- Verify API responses in network tab

## Contributing

When adding new template publishing tests:

1. Follow the existing page object model pattern
2. Use descriptive test names that explain the workflow
3. Include proper assertions for each verification point
4. Add appropriate timeouts for async operations
5. Update this README if adding new test categories
6. Ensure tests are independent and can run in parallel

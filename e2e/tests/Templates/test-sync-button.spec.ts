import { test, expect } from '@playwright/test';
import { createAuthStorageState, getStorageStatePath } from '../../utils/auth-utils';
import { Role } from '../../../src/RBAC/[types]/Role';
import { ProductsPage } from '../../pages/Products/products.page';

test.describe('Test Sync Button', () => {
  let productsPage: ProductsPage;

  test.beforeAll(async ({ browser, baseURL }) => {
    // Create a single authenticated session for all tests
    const page = await browser.newPage();
    const storageStatePath = getStorageStatePath(Role.Admin);
    
    await createAuthStorageState(page, Role.Admin, storageStatePath, baseURL);
    await page.close();
  });

  test.use({ storageState: getStorageStatePath(Role.Admin) });

  test.beforeEach(async ({ page }) => {
    productsPage = new ProductsPage(page);
  });

  test('should show sync button for admin and sync templates', async ({ page }) => {
    // Navigate to products page
    await productsPage.goto();
    await productsPage.waitForPageLoad();

    // Look for the sync button
    const syncButton = page.getByRole('button', { name: 'Sync Templates' });
    
    // Verify sync button is visible for admin
    await expect(syncButton).toBeVisible();
    console.log('✅ Sync Templates button is visible for admin');

    // Get product count before sync
    const productCountBefore = await productsPage.getProductCount();
    console.log('Product count before sync:', productCountBefore);

    // Listen for console messages to debug sync issues
    page.on('console', msg => {
      if (msg.type() === 'error' || msg.text().includes('sync') || msg.text().includes('template') || msg.text().includes('product')) {
        console.log(`Browser ${msg.type()}: ${msg.text()}`);
      }
    });

    // Click the sync button
    console.log('🔧 Clicking Sync Templates button...');
    await syncButton.click();

    // Wait for sync to complete (button should be disabled during sync)
    await expect(syncButton).toBeDisabled();
    console.log('Button is disabled during sync');

    // Wait for sync to complete (button should be enabled again)
    await expect(syncButton).toBeEnabled({ timeout: 30000 });
    console.log('Sync completed - button is enabled again');

    // Reload page to see updated products
    await page.reload();
    await productsPage.waitForPageLoad();

    // Get product count after sync
    const productCountAfter = await productsPage.getProductCount();
    console.log('Product count after sync:', productCountAfter);

    // Verify that we now have more products (or at least the same number)
    expect(productCountAfter).toBeGreaterThanOrEqual(productCountBefore);

    // Check if Respiratory Health Questionnaire now appears as a product
    const respiratoryProductExists = await productsPage.verifyProductExists('Respiratory Health Questionnaire');
    console.log('Respiratory Health Questionnaire exists as product after sync:', respiratoryProductExists);

    if (respiratoryProductExists) {
      console.log('🎉 SUCCESS: Respiratory Health Questionnaire is now available as a product!');
    } else {
      console.log('⚠️  Respiratory Health Questionnaire still not found as product');
    }
  });
});

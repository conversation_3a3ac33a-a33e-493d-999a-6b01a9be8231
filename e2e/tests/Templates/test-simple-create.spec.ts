import { test, expect } from '@playwright/test';
import { Role } from '../../../src/[types]/Role';
import { AllTemplatesPage } from '../../pages/Templates/all-templates.page';
import { CreateTemplatePage } from '../../pages/Templates/create-template.page';

test.describe('Simple Template Creation', () => {
  let allTemplatesPage: AllTemplatesPage;
  let createTemplatePage: CreateTemplatePage;

  test.beforeEach(async ({ page, baseURL }) => {
    allTemplatesPage = new AllTemplatesPage(page);
    createTemplatePage = new CreateTemplatePage(page);
    
    // Use direct login approach since auth state manager has issues
    const { loginAs } = await import('../../utils/auth-utils');
    await loginAs(page, Role.Admin, baseURL);
  });

  test('should be able to fill basic template details', async ({ page }) => {
    const templateName = `Simple Test Template ${Date.now()}`;
    
    await allTemplatesPage.goto();
    await allTemplatesPage.clickCreateTemplate();
    
    // Fill basic details without category
    await createTemplatePage.nameInput.fill(templateName);
    await createTemplatePage.descriptionInput.fill('Simple test description');
    
    // Fill estimated time
    const estimatedTimeInput = page.getByRole('spinbutton', { name: 'Estimated Time (minutes)' });
    await estimatedTimeInput.fill('10');
    
    console.log('Basic template details filled successfully');
    
    // Try to add a simple question
    await createTemplatePage.addQuestionButton.click();
    await page.waitForTimeout(2000);

    // Debug: Check what question inputs are available
    const questionInputs = await page.locator('input[name*="questions"][name*="text"]').all();
    console.log('Found question inputs:', questionInputs.length);

    if (questionInputs.length > 0) {
      const questionInput = questionInputs[0];
      const name = await questionInput.getAttribute('name');
      console.log('Question input name:', name);
      await questionInput.fill('Test question');
      console.log('Question filled successfully');

      // Verify the value was set
      const value = await questionInput.inputValue();
      console.log('Question input value after fill:', value);
    } else {
      console.log('No question inputs found, trying alternative approach');

      // Try to find any text input and fill it
      const textInputs = await page.locator('input[type="text"]').all();
      console.log('Found text inputs:', textInputs.length);

      for (const input of textInputs) {
        try {
          const name = await input.getAttribute('name');
          const placeholder = await input.getAttribute('placeholder');
          console.log('Text input:', { name, placeholder });

          if ((name && name.includes('question')) || (placeholder && placeholder.toLowerCase().includes('question'))) {
            await input.fill('Test question');
            console.log('Question added successfully via alternative method');
            break;
          }
        } catch {
          continue;
        }
      }
    }
    
    // Check form state before saving
    const nameValue = await createTemplatePage.nameInput.inputValue();
    const descValue = await createTemplatePage.descriptionInput.inputValue();
    console.log('Form values before save:', { name: nameValue, description: descValue });

    // Check if save button is enabled
    const saveButtonEnabled = await createTemplatePage.saveButton.isEnabled();
    console.log('Save button enabled:', saveButtonEnabled);

    // Try to save
    console.log('Clicking save button...');
    await createTemplatePage.saveButton.click();

    // Wait for either navigation or error state
    try {
      await Promise.race([
        page.waitForURL('**/templates', { timeout: 10000 }),
        page.waitForSelector('.MuiAlert-root', { timeout: 10000 }),
        page.waitForTimeout(3000)
      ]);
    } catch (error) {
      console.log('Wait failed:', error.message);
    }

    const currentUrl = page.url();
    console.log('Current URL after save:', currentUrl);

    if (currentUrl === 'http://localhost:3001/trq/templates') {
      console.log('Template creation successful - redirected to templates page');
    } else {
      console.log('Still on create page, checking for errors');

      // Check for any alerts or error messages
      const allAlerts = await page.locator('.MuiAlert-root').allTextContents();
      console.log('All alerts:', allAlerts);

      // Check for validation messages
      const validationMessages = await page.locator('.Mui-error, [aria-invalid="true"]').allTextContents();
      console.log('Validation messages:', validationMessages);

      // Check form state after save attempt
      const formData = await page.evaluate(() => {
        const form = document.querySelector('form');
        if (form) {
          const formData = new FormData(form);
          const data = {};
          for (let [key, value] of formData.entries()) {
            data[key] = value;
          }
          return data;
        }
        return null;
      });
      console.log('Form data:', formData);

      // Take a screenshot for debugging
      await page.screenshot({ path: 'debug-create-template.png' });
    }
  });
});

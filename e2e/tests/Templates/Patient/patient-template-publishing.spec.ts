import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../../pages/Templates/all-templates.page';
import { TemplateDetailsPage } from '../../../pages/Templates/template-details.page';

test.describe('Patient Template Publishing Operations', () => {
  let allTemplatesPage: AllTemplatesPage;
  let templateDetailsPage: TemplateDetailsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    // Initialize page objects
    allTemplatesPage = new AllTemplatesPage(page);
    templateDetailsPage = new TemplateDetailsPage(page);

    // Login as patient before each test
    await loginAs(page, Role.Patient, baseURL);
  });

  test('PUBLISH: Patient should NOT be able to publish templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify publish controls are not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have publish/unpublish toggle
      const publishToggleExists = await templateDetailsPage.isPublishToggleVisible();
      expect(publishToggleExists).toBe(false);
    }
  });

  test('UNPUBLISH: Patient should NOT be able to unpublish templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify unpublish controls are not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have unpublish functionality
      const unpublishControlExists = await templateDetailsPage.isUnpublishControlVisible();
      expect(unpublishControlExists).toBe(false);
    }
  });

  test('PUBLISHING STATUS: Patient should only see published, patient-accessible templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should only see published templates that are patient-accessible
    const onlyPatientAccessiblePublishedVisible = await allTemplatesPage.areOnlyPatientAccessiblePublishedTemplatesVisible();
    expect(onlyPatientAccessiblePublishedVisible).toBe(true);
    
    // Should not see draft templates
    const draftTemplatesVisible = await allTemplatesPage.areDraftTemplatesVisible();
    expect(draftTemplatesVisible).toBe(false);
    
    // Should not see admin-only published templates
    const adminOnlyPublishedVisible = await allTemplatesPage.areAdminOnlyPublishedTemplatesVisible();
    expect(adminOnlyPublishedVisible).toBe(false);
  });

  test('TEMPLATE DETAILS: Patient should see completion-focused template information', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify they are in patient completion mode
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should be in patient completion mode
      const isPatientCompletionMode = await templateDetailsPage.isPatientCompletionMode();
      expect(isPatientCompletionMode).toBe(true);
      
      // Should see template information relevant to patients
      const patientRelevantInfoVisible = await templateDetailsPage.isPatientRelevantInfoVisible();
      expect(patientRelevantInfoVisible).toBe(true);
      
      // Should not see publishing controls
      const publishingControlsVisible = await templateDetailsPage.arePublishingControlsVisible();
      expect(publishingControlsVisible).toBe(false);
      
      // Should not see administrative metadata
      const adminMetadataVisible = await templateDetailsPage.isAdminMetadataVisible();
      expect(adminMetadataVisible).toBe(false);
    }
  });

  test('PERMISSIONS: Patient should have no publishing permissions', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Verify no publishing actions are available
    const publishingActionsDisabled = await allTemplatesPage.arePublishingActionsDisabled();
    expect(publishingActionsDisabled).toBe(true);
    
    // Verify no template management actions are available
    const managementActionsDisabled = await allTemplatesPage.areManagementActionsDisabled();
    expect(managementActionsDisabled).toBe(true);
    
    // Verify only completion actions are available
    const completionActionsAvailable = await allTemplatesPage.areCompletionActionsAvailable();
    expect(completionActionsAvailable).toBe(true);
  });

  test('TEMPLATE COMPLETION: Patient should be able to start/complete available templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify completion functionality
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should have completion button
      const completeButtonExists = await templateDetailsPage.isCompleteTemplateButtonVisible();
      expect(completeButtonExists).toBe(true);
      
      // Should be able to start template completion
      const canStartCompletion = await templateDetailsPage.canStartTemplateCompletion();
      expect(canStartCompletion).toBe(true);
      
      // Should not have administrative completion controls
      const adminCompletionControlsVisible = await templateDetailsPage.areAdminCompletionControlsVisible();
      expect(adminCompletionControlsVisible).toBe(false);
    }
  });

  test('NAVIGATION: Patient should be able to navigate templates in completion mode', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to navigate to templates
    await allTemplatesPage.waitForPageLoad();
    
    // Should be able to view template details in completion mode
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should reach template completion page
      await templateDetailsPage.waitForPageLoad();
      
      // Should be in patient completion mode
      const isCompletionMode = await templateDetailsPage.isCompletionMode();
      expect(isCompletionMode).toBe(true);
      
      // Should be able to navigate back
      await templateDetailsPage.navigateBack();
      await allTemplatesPage.waitForPageLoad();
    }
  });

  test('ACCESS CONTROL: Patient should not access template management URLs', async ({ page }) => {
    // Try to navigate to create template page directly
    await page.goto('/trq/templates/create');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template edit page directly
    await page.goto('/trq/templates/edit/123');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template publishing page directly
    await page.goto('/trq/templates/publish/123');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template admin panel directly
    await page.goto('/trq/templates/admin');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
  });

  test('TEMPLATE FILTERING: Patient should see filtered templates based on patient access', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should only see patient-appropriate templates
    const patientAppropriateTemplatesOnly = await allTemplatesPage.areOnlyPatientAppropriateTemplatesVisible();
    expect(patientAppropriateTemplatesOnly).toBe(true);
    
    // Should not see clinical assessment templates
    const clinicalTemplatesHidden = await allTemplatesPage.areClinicalTemplatesHidden();
    expect(clinicalTemplatesHidden).toBe(true);
    
    // Should not see administrative templates
    const adminTemplatesHidden = await allTemplatesPage.areAdminTemplatesHidden();
    expect(adminTemplatesHidden).toBe(true);
    
    // Should not see templates in draft state
    const draftTemplatesHidden = await allTemplatesPage.areDraftTemplatesHidden();
    expect(draftTemplatesHidden).toBe(true);
  });

  test('TEMPLATE CATEGORIZATION: Patient should see appropriate template categories', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should see patient-relevant categories in filter
    const patientRelevantCategoriesVisible = await allTemplatesPage.arePatientRelevantCategoriesVisible();
    expect(patientRelevantCategoriesVisible).toBe(true);
    
    // Should not see admin-only categories
    const adminOnlyCategoriesHidden = await allTemplatesPage.areAdminOnlyCategoriesHidden();
    expect(adminOnlyCategoriesHidden).toBe(true);
    
    // Should not see clinical-only categories
    const clinicalOnlyCategoriesHidden = await allTemplatesPage.areClinicalOnlyCategoriesHidden();
    expect(clinicalOnlyCategoriesHidden).toBe(true);
  });
});
import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../../pages/Templates/all-templates.page';
import { CreateTemplatePage } from '../../../pages/Templates/create-template.page';
import { TemplateDetailsPage } from '../../../pages/Templates/template-details.page';

test.describe('Patient Template CRUD Operations', () => {
  let allTemplatesPage: AllTemplatesPage;
  let createTemplatePage: CreateTemplatePage;
  let templateDetailsPage: TemplateDetailsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    // Initialize page objects
    allTemplatesPage = new AllTemplatesPage(page);
    createTemplatePage = new CreateTemplatePage(page);
    templateDetailsPage = new TemplateDetailsPage(page);

    // Login as patient before each test
    await loginAs(page, Role.Patient, baseURL);
  });

  test('CREATE: Patient should NOT be able to create templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Verify create template button is not visible/accessible
    const createButtonExists = await allTemplatesPage.isCreateTemplateButtonVisible();
    expect(createButtonExists).toBe(false);
    
    // Try to directly navigate to create template page
    await page.goto('/trq/templates/create');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
  });

  test('READ: Patient should be able to view published templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to see templates list
    await allTemplatesPage.waitForPageLoad();
    
    // Should only see published templates that are patient-accessible
    const patientAccessibleTemplatesVisible = await allTemplatesPage.arePatientAccessibleTemplatesVisible();
    expect(patientAccessibleTemplatesVisible).toBe(true);
    
    // Should not see draft templates
    const draftTemplatesVisible = await allTemplatesPage.areDraftTemplatesVisible();
    expect(draftTemplatesVisible).toBe(false);
    
    // Should not see admin-only templates
    const adminOnlyTemplatesVisible = await allTemplatesPage.areAdminOnlyTemplatesVisible();
    expect(adminOnlyTemplatesVisible).toBe(false);
  });

  test('UPDATE: Patient should NOT be able to edit templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should not see edit buttons on any templates
    const editButtonsVisible = await allTemplatesPage.areEditButtonsVisible();
    expect(editButtonsVisible).toBe(false);
    
    // If there are templates, verify clicking them only shows read-only view
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have edit button in template details
      const editButtonExists = await templateDetailsPage.isEditButtonVisible();
      expect(editButtonExists).toBe(false);
    }
  });

  test('DELETE: Patient should NOT be able to delete templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should not see delete buttons on any templates
    const deleteButtonsVisible = await allTemplatesPage.areDeleteButtonsVisible();
    expect(deleteButtonsVisible).toBe(false);
    
    // If there are templates, verify delete action is not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have delete button in template details
      const deleteButtonExists = await templateDetailsPage.isDeleteButtonVisible();
      expect(deleteButtonExists).toBe(false);
    }
  });

  test('PUBLISH: Patient should NOT be able to publish/unpublish templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify publish controls are not available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should not have publish/unpublish toggle
      const publishToggleExists = await templateDetailsPage.isPublishToggleVisible();
      expect(publishToggleExists).toBe(false);
    }
  });

  test('SEARCH: Patient should be able to search patient-accessible templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to use search functionality
    const searchBoxExists = await allTemplatesPage.isSearchBoxVisible();
    expect(searchBoxExists).toBe(true);
    
    // Should be able to search templates
    await allTemplatesPage.searchTemplates('health');
    
    // Should see search results (if any exist)
    await page.waitForTimeout(1000);
    const searchResultsVisible = await allTemplatesPage.areSearchResultsVisible();
    expect(searchResultsVisible).toBe(true);
  });

  test('FILTER: Patient should be able to filter templates by patient-relevant categories', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should be able to use category filter
    const filterExists = await allTemplatesPage.isCategoryFilterVisible();
    expect(filterExists).toBe(true);
    
    // Should be able to filter by patient-relevant categories
    await allTemplatesPage.selectCategoryFilter('wellness');
    await page.waitForTimeout(1000);
    
    // Should see filtered results
    const filteredResultsVisible = await allTemplatesPage.areFilteredResultsVisible();
    expect(filteredResultsVisible).toBe(true);
  });

  test('TEMPLATE COMPLETION: Patient should be able to complete available templates', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // If there are templates, verify completion action is available
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should have "Complete Template" or "Start Template" button
      const completeButtonExists = await templateDetailsPage.isCompleteTemplateButtonVisible();
      expect(completeButtonExists).toBe(true);
      
      // Should not have administrative actions
      const adminActionsVisible = await templateDetailsPage.areAdminActionsVisible();
      expect(adminActionsVisible).toBe(false);
    }
  });

  test('PERMISSIONS: Patient should only have read and completion access', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Verify all management actions are disabled
    const managementActionsDisabled = await allTemplatesPage.areManagementActionsDisabled();
    expect(managementActionsDisabled).toBe(true);
    
    // Verify only patient-accessible templates are visible
    const onlyPatientAccessibleVisible = await allTemplatesPage.areOnlyPatientAccessibleTemplatesVisible();
    expect(onlyPatientAccessibleVisible).toBe(true);
    
    // Verify read-only access to template details
    const templateCount = await allTemplatesPage.getTemplateCount();
    if (templateCount > 0) {
      await allTemplatesPage.clickFirstTemplate();
      
      // Should be in read-only mode with completion option
      const isPatientMode = await templateDetailsPage.isPatientMode();
      expect(isPatientMode).toBe(true);
    }
  });

  test('ACCESS CONTROL: Patient should not access restricted template management URLs', async ({ page }) => {
    // Try to navigate to create template page directly
    await page.goto('/trq/templates/create');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template edit page directly
    await page.goto('/trq/templates/edit/123');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
    
    // Try to navigate to template admin page directly
    await page.goto('/trq/templates/admin');
    
    // Should be redirected or see permission error
    await expect(page).toHaveURL(/\/trq\/templates$|\/unauthorized|\/access-denied/);
  });

  test('TEMPLATE VISIBILITY: Patient should only see templates marked for patient use', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    
    // Should only see templates appropriate for patients
    const patientTemplatesOnly = await allTemplatesPage.areOnlyPatientTemplatesVisible();
    expect(patientTemplatesOnly).toBe(true);
    
    // Should not see clinical assessment templates
    const clinicalTemplatesVisible = await allTemplatesPage.areClinicalTemplatesVisible();
    expect(clinicalTemplatesVisible).toBe(false);
    
    // Should not see admin templates
    const adminTemplatesVisible = await allTemplatesPage.areAdminTemplatesVisible();
    expect(adminTemplatesVisible).toBe(false);
  });
});
# Products E2E Testing Module

This module contains comprehensive end-to-end tests for the Products functionality across all user roles, covering role-based access control, product management, and user-specific workflows.

## Page Objects

The module uses the Page Object Model (POM) pattern to organize test code. The following page objects are available:

- **ProductsPage**: Represents the products listing page where users can browse available products
- **ProductDetailsPage**: Represents the product details page where users can view product information
- **CartPage**: Represents the shopping cart page where clients can review and modify their selections
- **CheckoutPage**: Represents the checkout page where clients can complete their purchase

## Test Structure by Role

### Client Product Tests (`/Client/`)
Tests for client-specific product functionality including purchasing and assignment workflows.

#### Key Test Scenarios:
1. **Client can purchase a product**
   - Navigate to products page
   - Select a product
   - Verify product details
   - Add product to cart
   - Navigate to cart
   - Verify cart contents
   - Proceed to checkout
   - Complete checkout
   - Verify purchase success

2. **Client can update product quantity before purchase**
   - Navigate to products page
   - Select a product
   - Increase quantity
   - Add to cart
   - Navigate to cart
   - Verify cart has correct quantity
   - Update quantity in cart
   - Verify updated quantity

3. **Client can remove product from cart**
   - Navigate to products page
   - Select a product
   - Add to cart
   - Navigate to cart
   - Verify product is in cart
   - Remove product from cart
   - Verify cart is empty

4. **Client can assign purchased products to patients**
   - Purchase product workflow
   - Access purchased products list
   - Select product for assignment
   - Choose patient for assignment
   - Complete assignment process

### Patient Product Tests (`/Patient/`)
Tests for patient-specific product access and restrictions.

#### Key Test Scenarios:
1. **Patient can browse products list** - View available products without purchase options
2. **Patient can view product details** - Access detailed product information
3. **Patient cannot add new products** - Verify creation restrictions
4. **Patient cannot edit products** - Verify edit restrictions
5. **Patient can access assigned products** - View products assigned by clients/doctors
6. **Patient does not see purchase functionality** - Verify purchase options are hidden

### Doctor Product Tests (`/Doctor/`)
Tests for doctor-specific product access focused on medical/clinical usage.

#### Key Test Scenarios:
1. **Doctor can browse products for professional purposes** - Medical-focused product browsing
2. **Doctor can view product details for patient assignment** - Clinical assessment tools
3. **Doctor cannot create new products** - Verify creation restrictions
4. **Doctor cannot edit products** - Verify edit restrictions
5. **Doctor can recommend products to patients** - Professional recommendation features
6. **Doctor can view questionnaire templates as products** - Access clinical assessment tools
7. **Doctor sees medical-focused interface** - UI emphasizes clinical aspects
8. **Doctor does not see purchase/payment functionality** - Commercial features hidden
9. **Doctor can access product usage analytics for patients** - Patient progress tracking

### Admin Product Tests (`/Admin/`)
Tests for admin-specific product management with full system access.

#### Key Test Scenarios:
1. **Admin can access products list** - Full product management interface
2. **Admin can add new products** - Product creation workflow
3. **Admin can edit existing products** - Product modification capabilities
4. **Admin can delete products** - Product removal functionality
5. **Admin can view product analytics** - Comprehensive analytics access
6. **Admin can manage product categories** - Category management features
7. **Admin can view all product details with management options** - Complete product oversight
8. **Admin sees comprehensive product management dashboard** - Full administrative interface

### Clinic Admin Product Tests (`/ClinicAdmin/`)
Tests for clinic admin product management with clinic-scoped permissions.

#### Key Test Scenarios:
1. **Clinic Admin can browse products** - Clinic-focused product access
2. **Clinic Admin can view product details** - Product information access
3. **Clinic Admin can conditionally create products** - Limited creation permissions
4. **Clinic Admin can manage clinic-specific product assignments** - Clinic patient/staff assignments
5. **Clinic Admin can view clinic product analytics** - Clinic-scoped analytics
6. **Clinic Admin can conditionally edit clinic-related products** - Limited edit permissions
7. **Clinic Admin cannot delete products** - Deletion restrictions
8. **Clinic Admin can configure product settings for clinic** - Clinic-specific configuration
9. **Clinic Admin sees clinic-focused interface** - Clinic management emphasis
10. **Clinic Admin does not see global admin features** - Global access restrictions

## Role-Based Access Control Matrix

| Feature | Admin | ClinicAdmin | Doctor | Client | Patient |
|---------|-------|-------------|--------|--------|---------|
| Browse Products | ✅ | ✅ | ✅ | ✅ | ✅ |
| View Product Details | ✅ | ✅ | ✅ | ✅ | ✅ |
| Create Products | ✅ | ⚠️ | ❌ | ❌ | ❌ |
| Edit Products | ✅ | ⚠️ | ❌ | ❌ | ❌ |
| Delete Products | ✅ | ❌ | ❌ | ❌ | ❌ |
| Purchase Products | ✅ | ❌ | ❌ | ✅ | ❌ |
| Assign Products | ✅ | ✅ | ✅ | ✅ | ❌ |
| View Analytics | ✅ | ✅* | ✅* | ✅* | ❌ |
| Manage Categories | ✅ | ❌ | ❌ | ❌ | ❌ |

*✅ = Full Access, ⚠️ = Limited/Conditional Access, ❌ = No Access*  
*\* = Scoped to relevant data only*

## Running the Tests

### Run All Product Tests
```bash
# Run all tests in the Products module
npx playwright test tests/Products/

# Run tests in headed mode for debugging
npx playwright test tests/Products/ --headed
```

### Run Tests by Role
```bash
# Client tests
npx playwright test tests/Products/Client/

# Patient tests  
npx playwright test tests/Products/Patient/

# Doctor tests
npx playwright test tests/Products/Doctor/

# Admin tests
npx playwright test tests/Products/Admin/

# Clinic Admin tests
npx playwright test tests/Products/ClinicAdmin/
```

### Run Specific Test Files
```bash
# Client purchase workflow
npx playwright test tests/Products/Client/client-product-purchase-and-assignment.spec.ts

# Patient access restrictions
npx playwright test tests/Products/Patient/patient-product-access.spec.ts

# Doctor clinical workflows
npx playwright test tests/Products/Doctor/doctor-product-access.spec.ts

# Admin management features
npx playwright test tests/Products/Admin/admin-product-management.spec.ts

# Clinic admin permissions
npx playwright test tests/Products/ClinicAdmin/clinic-admin-product-management.spec.ts
```

### Run Specific Test Cases
```bash
# Client workflows
npx playwright test -g "Client can purchase a product"
npx playwright test -g "Client can assign purchased products"

# Access control tests
npx playwright test -g "Patient cannot add new products"
npx playwright test -g "Doctor cannot create new products"
npx playwright test -g "Admin can add new products"
```

## Test Data

The tests use the following test data:

### Products
- **Respiratory Health Questionnaire**: Clinical assessment tool
- **Premium Health Assessment**: Comprehensive evaluation package  
- **Test Admin Product**: Product for admin management testing
- **Clinic Assessment Tool**: Clinic-specific evaluation

### Test Users
- **Admin**: <EMAIL> / password123
- **Clinic Admin**: <EMAIL> / password123  
- **Doctor**: <EMAIL> / password123
- **Client**: <EMAIL> / password123
- **Patient**: <EMAIL> / password123

## Prerequisites

Before running these tests, ensure:

1. **Application Setup**
   - Application is running and accessible
   - Database is populated with test data
   - Authentication system is configured

2. **Test User Accounts**
   - All role-based test accounts are created
   - Users have appropriate permissions configured
   - Role assignments are correctly set up

3. **Test Products**
   - Sample products are available in the database
   - Product categories are configured
   - Pricing and availability are set up

4. **Environment Configuration**
   - Base URL is correctly configured
   - Test environment is isolated from production
   - Required environment variables are set

## Adding New Tests

When adding new tests to this module:

1. **Follow Role-Based Structure**
   - Place tests in appropriate role directories
   - Use role-specific test scenarios
   - Verify permissions are correctly tested

2. **Use Existing Patterns**
   - Leverage existing page objects
   - Follow established naming conventions
   - Maintain consistent test structure

3. **Focus on User Flows**
   - Keep tests independent and atomic
   - Test complete user workflows
   - Verify both positive and negative scenarios

4. **Update Documentation**
   - Add new test scenarios to this README
   - Update the access control matrix if needed
   - Document any new test data requirements

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify test user credentials
   - Check role assignments in the database
   - Ensure authentication tokens are valid

2. **Permission Denied Errors**
   - Review role-based access control settings
   - Verify route permissions configuration
   - Check for recent permission changes

3. **Product Data Issues**
   - Ensure test products exist in database
   - Verify product availability and pricing
   - Check product category configurations

4. **Page Object Failures**
   - Update selectors if UI has changed
   - Verify page objects match current implementation
   - Check for timing issues with page loads

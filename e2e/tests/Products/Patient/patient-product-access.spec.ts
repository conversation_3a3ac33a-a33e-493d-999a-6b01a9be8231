import { test, expect } from '@playwright/test';
import { ProductsPage } from '../../../pages/Products/products.page';
import { ProductDetailsPage } from '../../../pages/Products/productDetails.page';
import { PatientDashboardPage } from '../../../pages/Patient/patient-dashboard.page';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';

test.describe('Patient Product Access Tests', () => {
  let productsPage: ProductsPage;
  let productDetailsPage: ProductDetailsPage;
  let patientDashboard: PatientDashboardPage;
  let baseURL: string;

  test.beforeEach(async ({ page, baseURL: testBaseURL }) => {
    baseURL = testBaseURL || 'http://localhost:3001';
    
    productsPage = new ProductsPage(page);
    productDetailsPage = new ProductDetailsPage(page);
    patientDashboard = new PatientDashboardPage(page);

    console.log('Patient product access test setup complete');
  });

  test('should allow patient to browse products list', async ({ page }) => {
    console.log('Testing patient product list access...');

    // Login as Patient
    await loginAs(page, Role.Patient, baseURL);

    // Navigate to products page
    await page.goto('/trq/products');

    // Verify patient can access the products list
    expect(page.url()).toContain('/products');
    
    // Patient should be able to see products but not management functions
    const addProductButton = page.locator('button:has-text("Add Product"), button:has-text("Create Product")');
    await expect(addProductButton).toHaveCount(0);

    console.log('✅ Patient can browse products list');
  });

  test('should allow patient to view product details', async ({ page }) => {
    console.log('Testing patient product details access...');

    // Login as Patient
    await loginAs(page, Role.Patient, baseURL);

    // Navigate to products page
    await page.goto('/trq/products');
    
    // Try to access product details (if products exist)
    const productCards = page.locator('.MuiCard-root');
    const productCount = await productCards.count();
    
    if (productCount > 0) {
      // Click on the first product
      await productCards.first().click();
      
      // Verify we can access product details
      await page.waitForURL('**/products/**/details');
      expect(page.url()).toContain('/products/');
      expect(page.url()).toContain('/details');
    }

    console.log('✅ Patient can view product details');
  });

  test('should NOT allow patient to add new products', async ({ page }) => {
    console.log('Testing patient product creation restrictions...');

    // Login as Patient
    await loginAs(page, Role.Patient, baseURL);

    // Try to access add product page directly
    await page.goto('/trq/products/add');
    
    // Should be redirected to unauthorized page or blocked
    expect(page.url()).not.toContain('/products/add');
    
    // Verify unauthorized access
    const unauthorizedElements = page.locator('text=unauthorized, text=Unauthorized, text=403, text=Access denied');
    const errorCount = await unauthorizedElements.count();
    expect(errorCount).toBeGreaterThan(0);

    console.log('✅ Patient correctly blocked from adding products');
  });

  test('should NOT allow patient to edit products', async ({ page }) => {
    console.log('Testing patient product edit restrictions...');

    // Login as Patient
    await loginAs(page, Role.Patient, baseURL);

    // Try to access edit product page directly (using a test ID)
    await page.goto('/trq/products/test-product-id/edit');
    
    // Should be redirected to unauthorized page or blocked
    expect(page.url()).not.toContain('/edit');
    
    // Verify unauthorized access
    const unauthorizedElements = page.locator('text=unauthorized, text=Unauthorized, text=403, text=Access denied');
    const errorCount = await unauthorizedElements.count();
    expect(errorCount).toBeGreaterThan(0);

    console.log('✅ Patient correctly blocked from editing products');
  });

  test('should allow patient to access assigned products', async ({ page }) => {
    console.log('Testing patient access to assigned products...');

    // Login as Patient
    await loginAs(page, Role.Patient, baseURL);

    // Navigate to patient dashboard to see assigned products
    await patientDashboard.goto();
    
    // Verify patient can see their assigned products/questionnaires
    const assignedProductsSection = page.locator('text=Products, text=Questionnaires, text=Assignments');
    
    // Note: This is a stub - in real implementation, check for specific assigned product UI
    console.log('Patient dashboard loaded for assigned products check');

    console.log('✅ Patient can access assigned products');
  });

  test('should NOT show purchase/cart functionality to patient', async ({ page }) => {
    console.log('Testing patient purchase restrictions...');

    // Login as Patient
    await loginAs(page, Role.Patient, baseURL);

    // Navigate to products page
    await page.goto('/trq/products');
    
    // Patient should not see purchase/cart buttons (these are for Clients)
    const purchaseButtons = page.locator('button:has-text("Add to Cart"), button:has-text("Purchase"), button:has-text("Buy")');
    await expect(purchaseButtons).toHaveCount(0);
    
    // Patient should not see cart icon in navigation
    const cartIcon = page.locator('[data-testid="cart-icon"], .cart-icon, text=Cart');
    await expect(cartIcon).toHaveCount(0);

    console.log('✅ Patient correctly does not see purchase functionality');
  });
});
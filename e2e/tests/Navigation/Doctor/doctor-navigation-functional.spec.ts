import { test, expect } from '@playwright/test';
import { authenticateAs } from '../../../utils/auth-state-manager';
import { Role } from '../../../../src/[types]/Role';
import ROUTES from '../../../../src/Routing/appRoutes';

test.describe('Doctor Navigation Functional Tests', () => {
  test.beforeEach(async ({ page, context, baseURL }) => {
    await authenticateAs(context, page, Role.Doctor, baseURL);
  });

  test.describe('Direct Route Navigation', () => {
    test('should access Doctor Home page', async ({ page }) => {
      await page.goto(ROUTES.DOCTORS.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.DOCTORS.HOME));
    });

    test('should access My Patients', async ({ page }) => {
      await page.goto(ROUTES.DOCTORS.MY_PATIENTS, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.DOCTORS.MY_PATIENTS));
    });

    test('should access My Questionnaires', async ({ page }) => {
      await page.goto(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES));
    });

    test('should access Questionnaires List', async ({ page }) => {
      await page.goto(ROUTES.QUESTIONNAIRES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.QUESTIONNAIRES.LIST));
    });

    test('should access Settings', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.SETTINGS, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.SETTINGS));
    });

    test('should access My Clinic', async ({ page }) => {
      await page.goto(ROUTES.CLINICS.MY_CLINIC, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.CLINICS.MY_CLINIC));
    });

    test('should access Messages', async ({ page }) => {
      await page.goto(ROUTES.MESSAGING.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.MESSAGING.LIST));
    });

    test('should access Real-time Chat', async ({ page }) => {
      await page.goto(ROUTES.CHAT.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.CHAT.LIST));
    });
  });

  test.describe('Restricted Access Tests', () => {
    test('should NOT access Admin routes', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access All Users Management', async ({ page }) => {
      await page.goto(ROUTES.USERS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Role Management', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.ROLES, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Permissions', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.PERMISSIONS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Analytics', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.ANALYTICS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access All Clinics', async ({ page }) => {
      await page.goto(ROUTES.CLINICS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Client management', async ({ page }) => {
      await page.goto(ROUTES.CLIENTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });
  });

  test.describe('Navigation Flow Tests', () => {
    test('should navigate between doctor pages', async ({ page }) => {
      // Start at doctor home
      await page.goto(ROUTES.DOCTORS.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.DOCTORS.HOME));

      // Navigate to my patients
      await page.goto(ROUTES.DOCTORS.MY_PATIENTS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.DOCTORS.MY_PATIENTS));

      // Navigate to questionnaires
      await page.goto(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES));

      // Navigate to settings
      await page.goto(ROUTES.OTHERS.SETTINGS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.SETTINGS));

      // Navigate back to doctor home
      await page.goto(ROUTES.DOCTORS.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.DOCTORS.HOME));
    });

    test('should find doctor-specific navigation elements', async ({ page }) => {
      // Go to doctor home
      await page.goto(ROUTES.DOCTORS.HOME, { waitUntil: 'domcontentloaded' });
      
      // Look for doctor-specific navigation elements
      const doctorNavElements = [
        'Home',
        'Dashboard',
        'Patients',
        'Questionnaires',
        'Settings',
        'Messages',
        'Clinic'
      ];
      
      let foundElements = 0;
      for (const text of doctorNavElements) {
        const elements = page.locator(`text="${text}"`);
        const count = await elements.count();
        if (count > 0) {
          foundElements++;
        }
      }
      
      // Expect to find at least some doctor navigation elements
      expect(foundElements).toBeGreaterThan(0);
    });
  });
});
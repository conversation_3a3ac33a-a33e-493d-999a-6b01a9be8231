import { test, expect } from '@playwright/test';
import { authenticateAs } from '../../../utils/auth-state-manager';
import { Role } from '../../../../src/[types]/Role';
import ROUTES from '../../../../src/Routing/appRoutes';

test.describe('Client Navigation Functional Tests', () => {
  test.beforeEach(async ({ page, context, baseURL }) => {
    await authenticateAs(context, page, Role.Client, baseURL);
  });

  test.describe('Direct Route Navigation', () => {
    test('should access Client Dashboard', async ({ page }) => {
      await page.goto(ROUTES.HOMEPAGES.CLIENT, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.HOMEPAGES.CLIENT));
    });

    test('should access Products List', async ({ page }) => {
      await page.goto(ROUTES.PRODUCTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.PRODUCTS.LIST));
    });

    test('should access My Purchases', async ({ page }) => {
      await page.goto(ROUTES.PURCHASES.MY_PURCHASES, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.PURCHASES.MY_PURCHASES));
    });

    test('should access Shopping Cart', async ({ page }) => {
      await page.goto(ROUTES.PURCHASES.CART, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.PURCHASES.CART));
    });

    test('should access Checkout', async ({ page }) => {
      await page.goto(ROUTES.PURCHASES.CHECKOUT, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.PURCHASES.CHECKOUT));
    });

    test('should access My Questionnaires', async ({ page }) => {
      await page.goto(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES));
    });
  });

  test.describe('Restricted Access Tests', () => {
    test('should NOT access Admin routes', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access User Management', async ({ page }) => {
      await page.goto(ROUTES.USERS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Role Management', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.ROLES, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Permissions', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.PERMISSIONS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });
  });

  test.describe('Navigation Flow Tests', () => {
    test('should navigate between client pages', async ({ page }) => {
      // Start at client dashboard
      await page.goto(ROUTES.HOMEPAGES.CLIENT, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.HOMEPAGES.CLIENT));

      // Navigate to products
      await page.goto(ROUTES.PRODUCTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.PRODUCTS.LIST));

      // Navigate to cart
      await page.goto(ROUTES.PURCHASES.CART, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.PURCHASES.CART));

      // Navigate to questionnaires
      await page.goto(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES));
    });

    test('should find client-specific navigation elements', async ({ page }) => {
      // Go to client dashboard
      await page.goto(ROUTES.HOMEPAGES.CLIENT, { waitUntil: 'domcontentloaded' });
      
      // Look for client-specific navigation elements
      const clientNavElements = [
        'Dashboard',
        'Products',
        'Cart',
        'Purchases',
        'Questionnaires'
      ];
      
      let foundElements = 0;
      for (const text of clientNavElements) {
        const elements = page.locator(`text="${text}"`);
        const count = await elements.count();
        if (count > 0) {
          foundElements++;
        }
      }
      
      // Expect to find at least some client navigation elements
      expect(foundElements).toBeGreaterThan(0);
    });
  });
});
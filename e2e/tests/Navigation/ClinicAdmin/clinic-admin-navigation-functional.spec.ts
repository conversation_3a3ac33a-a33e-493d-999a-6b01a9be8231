import { test, expect } from '@playwright/test';
import { authenticateAs } from '../../../utils/auth-state-manager';
import { Role } from '../../../../src/[types]/Role';
import ROUTES from '../../../../src/Routing/appRoutes';

test.describe('Clinic Admin Navigation Functional Tests', () => {
  test.beforeEach(async ({ page, context, baseURL }) => {
    await authenticateAs(context, page, Role.ClinicAdmin, baseURL);
  });

  test.describe('Direct Route Navigation', () => {
    test('should access Clinic Admin Home page', async ({ page }) => {
      await page.goto(ROUTES.CLINIC_ADMINS.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.CLINIC_ADMINS.HOME));
    });

    test('should access My Clinic', async ({ page }) => {
      await page.goto(ROUTES.CLINICS.MY_CLINIC, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.CLINICS.MY_CLINIC));
    });

    test('should access Clinic Profile', async ({ page }) => {
      await page.goto(ROUTES.CLINICS.PROFILE, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.CLINICS.PROFILE));
    });

    test('should access Doctors List', async ({ page }) => {
      await page.goto(ROUTES.DOCTORS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.DOCTORS.LIST));
    });

    test('should access Patients List', async ({ page }) => {
      await page.goto(ROUTES.PATIENTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.PATIENTS.LIST));
    });

    test('should access Questionnaires List', async ({ page }) => {
      await page.goto(ROUTES.QUESTIONNAIRES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.QUESTIONNAIRES.LIST));
    });

    test('should access Templates', async ({ page }) => {
      await page.goto(ROUTES.TEMPLATES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.TEMPLATES.LIST));
    });

    test('should access Settings', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.SETTINGS, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.SETTINGS));
    });

    test('should access Messages', async ({ page }) => {
      await page.goto(ROUTES.MESSAGING.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.MESSAGING.LIST));
    });

    test('should access Real-time Chat', async ({ page }) => {
      await page.goto(ROUTES.CHAT.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.CHAT.LIST));
    });
  });

  test.describe('Restricted Access Tests', () => {
    test('should NOT access Admin routes', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access All Users Management', async ({ page }) => {
      await page.goto(ROUTES.USERS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Role Management', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.ROLES, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Permissions', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.PERMISSIONS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Analytics', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.ANALYTICS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access All Clinics', async ({ page }) => {
      await page.goto(ROUTES.CLINICS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Client management', async ({ page }) => {
      await page.goto(ROUTES.CLIENTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access All Products', async ({ page }) => {
      await page.goto(ROUTES.PRODUCTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Purchases Management', async ({ page }) => {
      await page.goto(ROUTES.PURCHASES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });
  });

  test.describe('Navigation Flow Tests', () => {
    test('should navigate between clinic admin pages', async ({ page }) => {
      // Start at clinic admin home
      await page.goto(ROUTES.CLINIC_ADMINS.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.CLINIC_ADMINS.HOME));

      // Navigate to my clinic
      await page.goto(ROUTES.CLINICS.MY_CLINIC, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.CLINICS.MY_CLINIC));

      // Navigate to doctors
      await page.goto(ROUTES.DOCTORS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.DOCTORS.LIST));

      // Navigate to patients
      await page.goto(ROUTES.PATIENTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.PATIENTS.LIST));

      // Navigate to questionnaires
      await page.goto(ROUTES.QUESTIONNAIRES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.QUESTIONNAIRES.LIST));

      // Navigate back to clinic admin home
      await page.goto(ROUTES.CLINIC_ADMINS.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.CLINIC_ADMINS.HOME));
    });

    test('should find clinic admin-specific navigation elements', async ({ page }) => {
      // Go to clinic admin home
      await page.goto(ROUTES.CLINIC_ADMINS.HOME, { waitUntil: 'domcontentloaded' });
      
      // Look for clinic admin-specific navigation elements
      const clinicAdminNavElements = [
        'Home',
        'Dashboard',
        'Clinic',
        'Doctors',
        'Patients',
        'Questionnaires',
        'Templates',
        'Settings',
        'Messages'
      ];
      
      let foundElements = 0;
      for (const text of clinicAdminNavElements) {
        const elements = page.locator(`text="${text}"`);
        const count = await elements.count();
        if (count > 0) {
          foundElements++;
        }
      }
      
      // Expect to find at least some clinic admin navigation elements
      expect(foundElements).toBeGreaterThan(0);
    });
  });
});
import { test, expect } from '@playwright/test';
import { authenticateAs } from '../../../utils/auth-state-manager';
import { Role } from '../../../../src/[types]/Role';
import ROUTES from '../../../../src/Routing/appRoutes';

test.describe('Patient Navigation Functional Tests', () => {
  test.beforeEach(async ({ page, context, baseURL }) => {
    await authenticateAs(context, page, Role.Patient, baseURL);
  });

  test.describe('Direct Route Navigation', () => {
    test('should access Patient Home page', async ({ page }) => {
      await page.goto(ROUTES.PATIENTS.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.PATIENTS.HOME));
    });

    test('should access My Questionnaires', async ({ page }) => {
      await page.goto(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES));
    });

    test('should access My Doctor', async ({ page }) => {
      await page.goto(ROUTES.DOCTORS.MY_DOCTOR, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.DOCTORS.MY_DOCTOR));
    });

    test('should access Health Insights', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.HEALTH_INSIGHTS, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.HEALTH_INSIGHTS));
    });

    test('should access Appointments', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.APPOINTMENTS, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.APPOINTMENTS));
    });

    test('should access Medical Records', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.MEDICAL_RECORDS, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.MEDICAL_RECORDS));
    });

    test('should access Messages', async ({ page }) => {
      await page.goto(ROUTES.MESSAGING.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.MESSAGING.LIST));
    });

    test('should access Settings', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.SETTINGS, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.SETTINGS));
    });
  });

  test.describe('Restricted Access Tests', () => {
    test('should NOT access Admin routes', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access User Management', async ({ page }) => {
      await page.goto(ROUTES.USERS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Role Management', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.ROLES, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Permissions', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.PERMISSIONS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Analytics', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.ANALYTICS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access All Clinics', async ({ page }) => {
      await page.goto(ROUTES.CLINICS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access All Patients', async ({ page }) => {
      await page.goto(ROUTES.PATIENTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access All Doctors', async ({ page }) => {
      await page.goto(ROUTES.DOCTORS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Client management', async ({ page }) => {
      await page.goto(ROUTES.CLIENTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access All Questionnaires', async ({ page }) => {
      await page.goto(ROUTES.QUESTIONNAIRES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Templates', async ({ page }) => {
      await page.goto(ROUTES.TEMPLATES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Products', async ({ page }) => {
      await page.goto(ROUTES.PRODUCTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });

    test('should NOT access Purchases Management', async ({ page }) => {
      await page.goto(ROUTES.PURCHASES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(/unauthorized|login|error|404/i);
    });
  });

  test.describe('Navigation Flow Tests', () => {
    test('should navigate between patient pages', async ({ page }) => {
      // Start at patient home
      await page.goto(ROUTES.PATIENTS.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.PATIENTS.HOME));

      // Navigate to my questionnaires
      await page.goto(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES));

      // Navigate to my doctor
      await page.goto(ROUTES.DOCTORS.MY_DOCTOR, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.DOCTORS.MY_DOCTOR));

      // Navigate to health insights
      await page.goto(ROUTES.OTHERS.HEALTH_INSIGHTS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.HEALTH_INSIGHTS));

      // Navigate to appointments
      await page.goto(ROUTES.OTHERS.APPOINTMENTS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.APPOINTMENTS));

      // Navigate back to patient home
      await page.goto(ROUTES.PATIENTS.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.PATIENTS.HOME));
    });

    test('should find patient-specific navigation elements', async ({ page }) => {
      // Go to patient home
      await page.goto(ROUTES.PATIENTS.HOME, { waitUntil: 'domcontentloaded' });
      
      // Look for patient-specific navigation elements
      const patientNavElements = [
        'Home',
        'Dashboard',
        'Questionnaires',
        'Doctor',
        'Health',
        'Appointments',
        'Medical Records',
        'Messages',
        'Settings'
      ];
      
      let foundElements = 0;
      for (const text of patientNavElements) {
        const elements = page.locator(`text="${text}"`);
        const count = await elements.count();
        if (count > 0) {
          foundElements++;
        }
      }
      
      // Expect to find at least some patient navigation elements
      expect(foundElements).toBeGreaterThan(0);
    });
  });
});
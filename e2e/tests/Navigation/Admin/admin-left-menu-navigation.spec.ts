import { test, expect } from '@playwright/test';
import { authenticateAs } from '../../../utils/auth-state-manager';
import { Role } from '../../../../src/[types]/Role';
import { MainLayoutPage } from '../../../pages/Layout/main-layout.page';
import ROUTES from '../../../../src/Routing/appRoutes';

test.describe('Admin Left Menu Navigation', () => {
  let mainLayoutPage: MainLayoutPage;

  test.beforeEach(async ({ page, context, baseURL }) => {
    mainLayoutPage = new MainLayoutPage(page);
    await authenticateAs(context, page, Role.Admin, baseURL);
    
    // Wait for page to load and then expand menu groups
    await page.waitForTimeout(2000);
    try {
      await mainLayoutPage.openAllMenuGroups();
    } catch (error) {
      console.log('Could not open menu groups:', error.message);
    }
  });

  test.describe('Debug Sidebar Structure', () => {
    test('should find and inspect sidebar elements', async ({ page }) => {
      // Debug: Check what sidebar elements exist
      const sidebarCount = await mainLayoutPage.sidebar.count();
      console.log(`Found ${sidebarCount} sidebar elements`);
      
      if (sidebarCount > 0) {
        const sidebarText = await mainLayoutPage.sidebar.first().innerText();
        console.log('Sidebar content:', sidebarText.substring(0, 500));
        
        // Look for any links or buttons in the sidebar
        const links = page.locator('a, button, [role="button"]');
        const linkCount = await links.count();
        console.log(`Found ${linkCount} clickable elements on page`);
        
        // Take a screenshot for debugging
        await page.screenshot({ path: 'debug-admin-sidebar.png' });
      }
      
      // This test should always pass - it's just for debugging
      expect(true).toBe(true);
    });
  });

  test.describe('Top Level Menu Items', () => {
    test('should navigate to Admin Home from menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('admin-home');
      await mainLayoutPage.verifyNavigation(ROUTES.ADMIN.HOME);
    });

    test('should navigate to Settings from menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('trq-settings');
      await mainLayoutPage.verifyNavigation(ROUTES.OTHERS.SETTINGS);
    });

    test('should navigate to Clinics from menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('clinics');
      await mainLayoutPage.verifyNavigation(ROUTES.CLINICS.LIST);
    });

    test('should navigate to Purchases from menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('purchases-list');
      await mainLayoutPage.verifyNavigation(ROUTES.PURCHASES.LIST);
    });

    test('should navigate to AI Chat from menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('ai-chat');
      await mainLayoutPage.verifyNavigation(ROUTES.AI_CHAT.BASE);
    });

    test('should navigate to Chat from menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('real-time-chat');
      await mainLayoutPage.verifyNavigation('/trq/chat');
    });
  });

  test.describe('Questionnaires Collapse Menu', () => {
    test('should expand questionnaires collapse and navigate to items', async ({ page }) => {
      // Verify collapse button is visible
      await expect(mainLayoutPage.sidebar.locator('[data-testid="nav-collapse-btn-questionnaires"]')).toBeVisible();
      
      // Navigate to All Questionnaires
      await mainLayoutPage.clickNavItem('questionnaires');
      await mainLayoutPage.verifyNavigation(ROUTES.QUESTIONNAIRES.LIST);
    });

    test('should navigate to Templates from questionnaires menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('templates');
      await mainLayoutPage.verifyNavigation(ROUTES.TEMPLATES.LIST);
    });
  });

  test.describe('Products Collapse Menu', () => {
    test('should expand products collapse and navigate to products list', async ({ page }) => {
      // Verify collapse button is visible
      await expect(mainLayoutPage.sidebar.locator('[data-testid="nav-collapse-btn-products"]')).toBeVisible();
      
      // Navigate to All Products
      await mainLayoutPage.clickNavItem('all-products');
      await mainLayoutPage.verifyNavigation(ROUTES.PRODUCTS.LIST);
    });
  });

  test.describe('User Management Collapse Menu', () => {
    test('should expand user management collapse and navigate to patients', async ({ page }) => {
      // Verify collapse button is visible
      await expect(mainLayoutPage.sidebar.locator('[data-testid="nav-collapse-btn-user-management"]')).toBeVisible();
      
      // Navigate to All Patients
      await mainLayoutPage.clickNavItem('all-patients');
      await mainLayoutPage.verifyNavigation(ROUTES.PATIENTS.LIST);
    });

    test('should navigate to Clients from user management menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('clients');
      await mainLayoutPage.verifyNavigation(ROUTES.CLIENTS.LIST);
    });

    test('should navigate to Doctors from user management menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('doctors');
      await mainLayoutPage.verifyNavigation(ROUTES.DOCTORS.LIST);
    });

    test('should navigate to Clinic Admins from user management menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('clinic-admins');
      await mainLayoutPage.verifyNavigation(ROUTES.CLINIC_ADMINS.LIST);
    });

    test('should navigate to All Users from user management menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('all-users');
      await mainLayoutPage.verifyNavigation(ROUTES.USERS.LIST);
    });

    test('should navigate to User Migration from user management menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('user-migration');
      await mainLayoutPage.verifyNavigation(ROUTES.USERS.MIGRATION);
    });
  });

  test.describe('Admin Collapse Menu', () => {
    test('should expand admin collapse and navigate to role management', async ({ page }) => {
      // Verify collapse button is visible
      await expect(mainLayoutPage.sidebar.locator('[data-testid="nav-collapse-btn-admin"]')).toBeVisible();
      
      // Navigate to Role Management
      await mainLayoutPage.clickNavItem('roles');
      await mainLayoutPage.verifyNavigation(ROUTES.ADMIN.ROLES);
    });

    test('should navigate to Permissions from admin menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('permissions');
      await mainLayoutPage.verifyNavigation(ROUTES.ADMIN.PERMISSIONS);
    });

    test('should navigate to Analytics from admin menu', async ({ page }) => {
      await mainLayoutPage.clickNavItem('site-analytics');
      await mainLayoutPage.verifyNavigation(ROUTES.OTHERS.ANALYTICS);
    });
  });

  test.describe('Menu Visibility Tests', () => {
    test('should show all expected menu items for Admin role', async ({ page }) => {
      // Top level items
      await expect(mainLayoutPage.getNavItem('admin-home')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('trq-settings')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('clinics')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('purchases-list')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('ai-chat')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('real-time-chat')).toBeVisible();

      // Collapse buttons
      await expect(mainLayoutPage.sidebar.locator('[data-testid="nav-collapse-btn-questionnaires"]')).toBeVisible();
      await expect(mainLayoutPage.sidebar.locator('[data-testid="nav-collapse-btn-products"]')).toBeVisible();
      await expect(mainLayoutPage.sidebar.locator('[data-testid="nav-collapse-btn-user-management"]')).toBeVisible();
      await expect(mainLayoutPage.sidebar.locator('[data-testid="nav-collapse-btn-admin"]')).toBeVisible();
    });

    test('should show all expected sub-menu items when expanded', async ({ page }) => {
      // Questionnaires sub-items
      await expect(mainLayoutPage.getNavItem('questionnaires')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('templates')).toBeVisible();

      // Products sub-items
      await expect(mainLayoutPage.getNavItem('all-products')).toBeVisible();

      // User Management sub-items
      await expect(mainLayoutPage.getNavItem('all-patients')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('clients')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('doctors')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('clinic-admins')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('all-users')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('user-migration')).toBeVisible();

      // Admin sub-items
      await expect(mainLayoutPage.getNavItem('roles')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('permissions')).toBeVisible();
      await expect(mainLayoutPage.getNavItem('site-analytics')).toBeVisible();
    });
  });
});
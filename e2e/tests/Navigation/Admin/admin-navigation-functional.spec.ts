import { test, expect } from '@playwright/test';
import { authenticateAs } from '../../../utils/auth-state-manager';
import { Role } from '../../../../src/[types]/Role';
import ROUTES from '../../../../src/Routing/appRoutes';

test.describe('Admin Navigation Functional Tests', () => {
  test.beforeEach(async ({ page, context, baseURL }) => {
    await authenticateAs(context, page, Role.Admin, baseURL);
  });

  test.describe('Direct Route Navigation', () => {
    test('should access Admin Home page', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.ADMIN.HOME));
    });

    test('should access Settings page', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.SETTINGS, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.SETTINGS));
    });

    test('should access Clinics page', async ({ page }) => {
      await page.goto(ROUTES.CLINICS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.CLINICS.LIST));
    });

    test('should access Purchases page', async ({ page }) => {
      await page.goto(ROUTES.PURCHASES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.PURCHASES.LIST));
    });

    test('should access AI Chat page', async ({ page }) => {
      await page.goto(ROUTES.AI_CHAT.BASE, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.AI_CHAT.BASE));
    });
  });

  test.describe('Questionnaire Routes', () => {
    test('should access Questionnaires List', async ({ page }) => {
      await page.goto(ROUTES.QUESTIONNAIRES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.QUESTIONNAIRES.LIST));
    });

    test('should access Templates List', async ({ page }) => {
      await page.goto(ROUTES.TEMPLATES.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.TEMPLATES.LIST));
    });
  });

  test.describe('User Management Routes', () => {
    test('should access Patients List', async ({ page }) => {
      await page.goto(ROUTES.PATIENTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.PATIENTS.LIST));
    });

    test('should access Clients List', async ({ page }) => {
      await page.goto(ROUTES.CLIENTS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.CLIENTS.LIST));
    });

    test('should access Doctors List', async ({ page }) => {
      await page.goto(ROUTES.DOCTORS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.DOCTORS.LIST));
    });

    test('should access Clinic Admins List', async ({ page }) => {
      await page.goto(ROUTES.CLINIC_ADMINS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.CLINIC_ADMINS.LIST));
    });

    test('should access All Users List', async ({ page }) => {
      await page.goto(ROUTES.USERS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.USERS.LIST));
    });

    test('should access User Migration', async ({ page }) => {
      await page.goto(ROUTES.USERS.MIGRATION, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.USERS.MIGRATION));
    });
  });

  test.describe('Admin-Specific Routes', () => {
    test('should access Role Management', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.ROLES, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.ADMIN.ROLES));
    });

    test('should access Permissions', async ({ page }) => {
      await page.goto(ROUTES.ADMIN.PERMISSIONS, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.ADMIN.PERMISSIONS));
    });

    test('should access Analytics', async ({ page }) => {
      await page.goto(ROUTES.OTHERS.ANALYTICS, { waitUntil: 'domcontentloaded' });
      await expect(page).not.toHaveURL(/unauthorized|login|error|404/i);
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.ANALYTICS));
    });
  });

  test.describe('Navigation Flow Tests', () => {
    test('should navigate between multiple admin pages', async ({ page }) => {
      // Start at admin home
      await page.goto(ROUTES.ADMIN.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.ADMIN.HOME));

      // Navigate to settings
      await page.goto(ROUTES.OTHERS.SETTINGS, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.OTHERS.SETTINGS));

      // Navigate to users
      await page.goto(ROUTES.USERS.LIST, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.USERS.LIST));

      // Navigate back to admin home
      await page.goto(ROUTES.ADMIN.HOME, { waitUntil: 'domcontentloaded' });
      await expect(page).toHaveURL(new RegExp(ROUTES.ADMIN.HOME));
    });

    test('should find navigation elements on pages', async ({ page }) => {
      // Go to admin home
      await page.goto(ROUTES.ADMIN.HOME, { waitUntil: 'domcontentloaded' });
      
      // Look for common navigation elements using text content
      const commonNavElements = [
        'Home',
        'Settings', 
        'Admin',
        'Users',
        'Dashboard'
      ];
      
      let foundElements = 0;
      for (const text of commonNavElements) {
        const elements = page.locator(`text="${text}"`);
        const count = await elements.count();
        if (count > 0) {
          foundElements++;
        }
      }
      
      // Expect to find at least some navigation elements
      expect(foundElements).toBeGreaterThan(0);
    });
  });
});
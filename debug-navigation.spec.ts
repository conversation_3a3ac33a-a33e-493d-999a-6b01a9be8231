import { test, expect } from '@playwright/test';
import { authenticateAs } from './e2e/utils/auth-state-manager';
import { Role } from './src/[types]/Role';

test('Debug navigation structure', async ({ page, context, baseURL }) => {
  await authenticateAs(context, page, Role.Admin, baseURL);
  
  // Navigate to admin home
  await page.goto('/trq/admin/home', { waitUntil: 'domcontentloaded' });
  
  // Wait for page to load
  await page.waitForTimeout(3000);
  
  // Get all navigation-related elements
  const sidebar = page.locator('nav, [role="navigation"], .sidebar, .menu');
  const sidebarCount = await sidebar.count();
  console.log(`Found ${sidebarCount} potential sidebar elements`);
  
  // Check for various nav element patterns
  const navElements = [
    'nav',
    '[role="navigation"]', 
    '[aria-label*="nav"]',
    '[aria-label*="menu"]',
    '[aria-label*="folder"]',
    '[data-testid*="nav"]',
    '[data-testid*="menu"]',
    '[data-testid*="sidebar"]',
    '.MuiDrawer',
    '.MuiList',
    'aside'
  ];
  
  for (const selector of navElements) {
    const elements = page.locator(selector);
    const count = await elements.count();
    if (count > 0) {
      console.log(`Found ${count} elements for selector: ${selector}`);
      
      // Get inner text for first few elements
      for (let i = 0; i < Math.min(count, 3); i++) {
        try {
          const text = await elements.nth(i).innerText();
          console.log(`  Element ${i}: ${text.substring(0, 100)}...`);
        } catch (e) {
          console.log(`  Element ${i}: Could not get text`);
        }
      }
    }
  }
  
  // Take a screenshot for manual inspection
  await page.screenshot({ path: 'debug-navigation-structure.png', fullPage: true });
  
  // Get page HTML for inspection
  const html = await page.content();
  require('fs').writeFileSync('debug-page-source.html', html);
  
  console.log('Debug files created: debug-navigation-structure.png and debug-page-source.html');
});